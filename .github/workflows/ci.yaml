name: CI

on:
  push:
    branches:
      - master
      - release-*
  pull_request: {}
  workflow_dispatch: {}

env:
  # Common versions
  GO_VERSION: '1.22'
  GOLANGCI_VERSION: 'v1.58'
  DOCKER_BUILDX_VERSION: 'v0.4.2'

  # Common users. We can't run a step 'if secrets.AWS_USR != ""' but we can run
  # a step 'if env.AWS_USR' != ""', so we copy these to succinctly test whether
  # credentials have been provided before trying to run steps that need them.
  DOCKER_USR: ${{ secrets.DOCKER_USR }}
  AWS_USR: ${{ secrets.AWS_USR }}

jobs:

  golangci-lint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: true
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Cache Go Dependencies
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-
      - name: Code generate
        run: |
          make generate
      - name: Lint golang code
        uses: golangci/golangci-lint-action@v3.5.0
        with:
          version: ${{ env.GOLANGCI_VERSION }}
          args: --verbose --timeout=10m
          skip-pkg-cache: true

  unit-tests:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: true
      - name: Fetch History
        run: git fetch --prune --unshallow
      - name: Setup Go
        uses: actions/setup-go@v3
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Cache Go Dependencies
        uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-
      - name: Run Unit Tests
        run: |
          make test
          git status
      - name: Publish Unit Test Coverage
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: unittests
          file: cover.out