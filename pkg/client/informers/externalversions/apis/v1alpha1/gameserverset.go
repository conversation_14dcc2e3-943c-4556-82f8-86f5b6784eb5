/*
Copyright 2022 The Kruise Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	time "time"

	apisv1alpha1 "github.com/openkruise/kruise-game/apis/v1alpha1"
	versioned "github.com/openkruise/kruise-game/pkg/client/clientset/versioned"
	internalinterfaces "github.com/openkruise/kruise-game/pkg/client/informers/externalversions/internalinterfaces"
	v1alpha1 "github.com/openkruise/kruise-game/pkg/client/listers/apis/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// GameServerSetInformer provides access to a shared informer and lister for
// GameServerSets.
type GameServerSetInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1alpha1.GameServerSetLister
}

type gameServerSetInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewGameServerSetInformer constructs a new informer for GameServerSet type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewGameServerSetInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredGameServerSetInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredGameServerSetInformer constructs a new informer for GameServerSet type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredGameServerSetInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.GameV1alpha1().GameServerSets(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.GameV1alpha1().GameServerSets(namespace).Watch(context.TODO(), options)
			},
		},
		&apisv1alpha1.GameServerSet{},
		resyncPeriod,
		indexers,
	)
}

func (f *gameServerSetInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredGameServerSetInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *gameServerSetInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&apisv1alpha1.GameServerSet{}, f.defaultInformer)
}

func (f *gameServerSetInformer) Lister() v1alpha1.GameServerSetLister {
	return v1alpha1.NewGameServerSetLister(f.Informer().GetIndexer())
}
