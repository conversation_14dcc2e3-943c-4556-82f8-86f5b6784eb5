/*
Copyright 2022 The Kruise Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "github.com/openkruise/kruise-game/apis/v1alpha1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
)

// GameServerSetLister helps list GameServerSets.
// All objects returned here must be treated as read-only.
type GameServerSetLister interface {
	// List lists all GameServerSets in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.GameServerSet, err error)
	// GameServerSets returns an object that can list and get GameServerSets.
	GameServerSets(namespace string) GameServerSetNamespaceLister
	GameServerSetListerExpansion
}

// gameServerSetLister implements the GameServerSetLister interface.
type gameServerSetLister struct {
	indexer cache.Indexer
}

// NewGameServerSetLister returns a new GameServerSetLister.
func NewGameServerSetLister(indexer cache.Indexer) GameServerSetLister {
	return &gameServerSetLister{indexer: indexer}
}

// List lists all GameServerSets in the indexer.
func (s *gameServerSetLister) List(selector labels.Selector) (ret []*v1alpha1.GameServerSet, err error) {
	err = cache.ListAll(s.indexer, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.GameServerSet))
	})
	return ret, err
}

// GameServerSets returns an object that can list and get GameServerSets.
func (s *gameServerSetLister) GameServerSets(namespace string) GameServerSetNamespaceLister {
	return gameServerSetNamespaceLister{indexer: s.indexer, namespace: namespace}
}

// GameServerSetNamespaceLister helps list and get GameServerSets.
// All objects returned here must be treated as read-only.
type GameServerSetNamespaceLister interface {
	// List lists all GameServerSets in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1alpha1.GameServerSet, err error)
	// Get retrieves the GameServerSet from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1alpha1.GameServerSet, error)
	GameServerSetNamespaceListerExpansion
}

// gameServerSetNamespaceLister implements the GameServerSetNamespaceLister
// interface.
type gameServerSetNamespaceLister struct {
	indexer   cache.Indexer
	namespace string
}

// List lists all GameServerSets in the indexer for a given namespace.
func (s gameServerSetNamespaceLister) List(selector labels.Selector) (ret []*v1alpha1.GameServerSet, err error) {
	err = cache.ListAllByNamespace(s.indexer, s.namespace, selector, func(m interface{}) {
		ret = append(ret, m.(*v1alpha1.GameServerSet))
	})
	return ret, err
}

// Get retrieves the GameServerSet from the indexer for a given namespace and name.
func (s gameServerSetNamespaceLister) Get(name string) (*v1alpha1.GameServerSet, error) {
	obj, exists, err := s.indexer.GetByKey(s.namespace + "/" + name)
	if err != nil {
		return nil, err
	}
	if !exists {
		return nil, errors.NewNotFound(v1alpha1.Resource("gameserverset"), name)
	}
	return obj.(*v1alpha1.GameServerSet), nil
}
