---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: gameserversets.game.kruise.io
spec:
  group: game.kruise.io
  names:
    kind: GameServerSet
    listKind: GameServerSetList
    plural: gameserversets
    shortNames:
    - gss
    singular: gameserverset
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The desired number of GameServers.
      jsonPath: .spec.replicas
      name: DESIRED
      type: integer
    - description: The number of currently all GameServers.
      jsonPath: .status.currentReplicas
      name: CURRENT
      type: integer
    - description: The number of GameServers updated.
      jsonPath: .status.updatedReplicas
      name: UPDATED
      type: integer
    - description: The number of GameServers ready.
      jsonPath: .status.readyReplicas
      name: READY
      type: integer
    - description: The number of GameServers Maintaining.
      jsonPath: .status.maintainingReplicas
      name: Maintaining
      type: integer
    - description: The number of GameServers WaitToBeDeleted.
      jsonPath: .status.waitToBeDeletedReplicas
      name: WaitToBeDeleted
      type: integer
    - description: The number of GameServers PreDelete.
      jsonPath: .status.preDeleteReplicas
      name: PreDelete
      type: integer
    - description: The age of GameServerSet.
      jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: GameServerSet is the Schema for the gameserversets API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: GameServerSetSpec defines the desired state of GameServerSet
            properties:
              gameServerTemplate:
                description: |-
                  INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                properties:
                  reclaimPolicy:
                    description: |-
                      ReclaimPolicy indicates the reclaim policy for GameServer.
                      Default is Cascade.
                    type: string
                  volumeClaimTemplates:
                    items:
                      description: PersistentVolumeClaim is a user's request for and
                        claim to a persistent volume
                      properties:
                        apiVersion:
                          description: |-
                            APIVersion defines the versioned schema of this representation of an object.
                            Servers should convert recognized schemas to the latest internal value, and
                            may reject unrecognized values.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
                          type: string
                        kind:
                          description: |-
                            Kind is a string value representing the REST resource this object represents.
                            Servers may infer this from the endpoint the client submits requests to.
                            Cannot be updated.
                            In CamelCase.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                          type: string
                        metadata:
                          description: |-
                            Standard object's metadata.
                            More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
                          properties:
                            annotations:
                              additionalProperties:
                                type: string
                              type: object
                            finalizers:
                              items:
                                type: string
                              type: array
                            labels:
                              additionalProperties:
                                type: string
                              type: object
                            name:
                              type: string
                            namespace:
                              type: string
                          type: object
                        spec:
                          description: |-
                            spec defines the desired characteristics of a volume requested by a pod author.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          properties:
                            accessModes:
                              description: |-
                                accessModes contains the desired access modes the volume should have.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            dataSource:
                              description: |-
                                dataSource field can be used to specify either:
                                * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot)
                                * An existing PVC (PersistentVolumeClaim)
                                If the provisioner or an external controller can support the specified data source,
                                it will create a new volume based on the contents of the specified data source.
                                When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef,
                                and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified.
                                If the namespace is specified, then dataSourceRef will not be copied to dataSource.
                              properties:
                                apiGroup:
                                  description: |-
                                    APIGroup is the group for the resource being referenced.
                                    If APIGroup is not specified, the specified Kind must be in the core API group.
                                    For any other third-party types, APIGroup is required.
                                  type: string
                                kind:
                                  description: Kind is the type of resource being
                                    referenced
                                  type: string
                                name:
                                  description: Name is the name of resource being
                                    referenced
                                  type: string
                              required:
                              - kind
                              - name
                              type: object
                              x-kubernetes-map-type: atomic
                            dataSourceRef:
                              description: |-
                                dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
                                volume is desired. This may be any object from a non-empty API group (non
                                core object) or a PersistentVolumeClaim object.
                                When this field is specified, volume binding will only succeed if the type of
                                the specified object matches some installed volume populator or dynamic
                                provisioner.
                                This field will replace the functionality of the dataSource field and as such
                                if both fields are non-empty, they must have the same value. For backwards
                                compatibility, when namespace isn't specified in dataSourceRef,
                                both fields (dataSource and dataSourceRef) will be set to the same
                                value automatically if one of them is empty and the other is non-empty.
                                When namespace is specified in dataSourceRef,
                                dataSource isn't set to the same value and must be empty.
                                There are three important differences between dataSource and dataSourceRef:
                                * While dataSource only allows two specific types of objects, dataSourceRef
                                  allows any non-core object, as well as PersistentVolumeClaim objects.
                                * While dataSource ignores disallowed values (dropping them), dataSourceRef
                                  preserves all values, and generates an error if a disallowed value is
                                  specified.
                                * While dataSource only allows local objects, dataSourceRef allows objects
                                  in any namespaces.
                                (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled.
                                (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                              properties:
                                apiGroup:
                                  description: |-
                                    APIGroup is the group for the resource being referenced.
                                    If APIGroup is not specified, the specified Kind must be in the core API group.
                                    For any other third-party types, APIGroup is required.
                                  type: string
                                kind:
                                  description: Kind is the type of resource being
                                    referenced
                                  type: string
                                name:
                                  description: Name is the name of resource being
                                    referenced
                                  type: string
                                namespace:
                                  description: |-
                                    Namespace is the namespace of resource being referenced
                                    Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details.
                                    (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.
                                  type: string
                              required:
                              - kind
                              - name
                              type: object
                            resources:
                              description: |-
                                resources represents the minimum resources the volume should have.
                                If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements
                                that are lower than previous value but must still be higher than capacity recorded in the
                                status field of the claim.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources
                              properties:
                                limits:
                                  additionalProperties:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  description: |-
                                    Limits describes the maximum amount of compute resources allowed.
                                    More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                  type: object
                                requests:
                                  additionalProperties:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  description: |-
                                    Requests describes the minimum amount of compute resources required.
                                    If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                    otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                    More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                  type: object
                              type: object
                            selector:
                              description: selector is a label query over volumes
                                to consider for binding.
                              properties:
                                matchExpressions:
                                  description: matchExpressions is a list of label
                                    selector requirements. The requirements are ANDed.
                                  items:
                                    description: |-
                                      A label selector requirement is a selector that contains values, a key, and an operator that
                                      relates the key and values.
                                    properties:
                                      key:
                                        description: key is the label key that the
                                          selector applies to.
                                        type: string
                                      operator:
                                        description: |-
                                          operator represents a key's relationship to a set of values.
                                          Valid operators are In, NotIn, Exists and DoesNotExist.
                                        type: string
                                      values:
                                        description: |-
                                          values is an array of string values. If the operator is In or NotIn,
                                          the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                          the values array must be empty. This array is replaced during a strategic
                                          merge patch.
                                        items:
                                          type: string
                                        type: array
                                        x-kubernetes-list-type: atomic
                                    required:
                                    - key
                                    - operator
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                matchLabels:
                                  additionalProperties:
                                    type: string
                                  description: |-
                                    matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                                    map is equivalent to an element of matchExpressions, whose key field is "key", the
                                    operator is "In", and the values array contains only "value". The requirements are ANDed.
                                  type: object
                              type: object
                              x-kubernetes-map-type: atomic
                            storageClassName:
                              description: |-
                                storageClassName is the name of the StorageClass required by the claim.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
                              type: string
                            volumeAttributesClassName:
                              description: |-
                                volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim.
                                If specified, the CSI driver will create or update the volume with the attributes defined
                                in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName,
                                it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass
                                will be applied to the claim but it's not allowed to reset this field to empty string once it is set.
                                If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass
                                will be set by the persistentvolume controller if it exists.
                                If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be
                                set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource
                                exists.
                                More info: https://kubernetes.io/docs/concepts/storage/volume-attributes-classes/
                                (Alpha) Using this field requires the VolumeAttributesClass feature gate to be enabled.
                              type: string
                            volumeMode:
                              description: |-
                                volumeMode defines what type of volume is required by the claim.
                                Value of Filesystem is implied when not included in claim spec.
                              type: string
                            volumeName:
                              description: volumeName is the binding reference to
                                the PersistentVolume backing this claim.
                              type: string
                          type: object
                        status:
                          description: |-
                            status represents the current information/status of a persistent volume claim.
                            Read-only.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          properties:
                            accessModes:
                              description: |-
                                accessModes contains the actual access modes the volume backing the PVC has.
                                More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            allocatedResourceStatuses:
                              additionalProperties:
                                description: |-
                                  When a controller receives persistentvolume claim update with ClaimResourceStatus for a resource
                                  that it does not recognizes, then it should ignore that update and let other controllers
                                  handle it.
                                type: string
                              description: "allocatedResourceStatuses stores status
                                of resource being resized for the given PVC.\nKey
                                names follow standard Kubernetes label syntax. Valid
                                values are either:\n\t* Un-prefixed keys:\n\t\t- storage
                                - the capacity of the volume.\n\t* Custom resources
                                must use implementation-defined prefixed names such
                                as \"example.com/my-custom-resource\"\nApart from
                                above values - keys that are unprefixed or have kubernetes.io
                                prefix are considered\nreserved and hence may not
                                be used.\n\nClaimResourceStatus can be in any of following
                                states:\n\t- ControllerResizeInProgress:\n\t\tState
                                set when resize controller starts resizing the volume
                                in control-plane.\n\t- ControllerResizeFailed:\n\t\tState
                                set when resize has failed in resize controller with
                                a terminal error.\n\t- NodeResizePending:\n\t\tState
                                set when resize controller has finished resizing the
                                volume but further resizing of\n\t\tvolume is needed
                                on the node.\n\t- NodeResizeInProgress:\n\t\tState
                                set when kubelet starts resizing the volume.\n\t-
                                NodeResizeFailed:\n\t\tState set when resizing has
                                failed in kubelet with a terminal error. Transient
                                errors don't set\n\t\tNodeResizeFailed.\nFor example:
                                if expanding a PVC for more capacity - this field
                                can be one of the following states:\n\t- pvc.status.allocatedResourceStatus['storage']
                                = \"ControllerResizeInProgress\"\n     - pvc.status.allocatedResourceStatus['storage']
                                = \"ControllerResizeFailed\"\n     - pvc.status.allocatedResourceStatus['storage']
                                = \"NodeResizePending\"\n     - pvc.status.allocatedResourceStatus['storage']
                                = \"NodeResizeInProgress\"\n     - pvc.status.allocatedResourceStatus['storage']
                                = \"NodeResizeFailed\"\nWhen this field is not set,
                                it means that no resize operation is in progress for
                                the given PVC.\n\nA controller that receives PVC update
                                with previously unknown resourceName or ClaimResourceStatus\nshould
                                ignore the update for the purpose it was designed.
                                For example - a controller that\nonly is responsible
                                for resizing capacity of the volume, should ignore
                                PVC updates that change other valid\nresources associated
                                with PVC.\n\nThis is an alpha field and requires enabling
                                RecoverVolumeExpansionFailure feature."
                              type: object
                              x-kubernetes-map-type: granular
                            allocatedResources:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: "allocatedResources tracks the resources
                                allocated to a PVC including its capacity.\nKey names
                                follow standard Kubernetes label syntax. Valid values
                                are either:\n\t* Un-prefixed keys:\n\t\t- storage
                                - the capacity of the volume.\n\t* Custom resources
                                must use implementation-defined prefixed names such
                                as \"example.com/my-custom-resource\"\nApart from
                                above values - keys that are unprefixed or have kubernetes.io
                                prefix are considered\nreserved and hence may not
                                be used.\n\nCapacity reported here may be larger than
                                the actual capacity when a volume expansion operation\nis
                                requested.\nFor storage quota, the larger value from
                                allocatedResources and PVC.spec.resources is used.\nIf
                                allocatedResources is not set, PVC.spec.resources
                                alone is used for quota calculation.\nIf a volume
                                expansion capacity request is lowered, allocatedResources
                                is only\nlowered if there are no expansion operations
                                in progress and if the actual volume capacity\nis
                                equal or lower than the requested capacity.\n\nA controller
                                that receives PVC update with previously unknown resourceName\nshould
                                ignore the update for the purpose it was designed.
                                For example - a controller that\nonly is responsible
                                for resizing capacity of the volume, should ignore
                                PVC updates that change other valid\nresources associated
                                with PVC.\n\nThis is an alpha field and requires enabling
                                RecoverVolumeExpansionFailure feature."
                              type: object
                            capacity:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: capacity represents the actual resources
                                of the underlying volume.
                              type: object
                            conditions:
                              description: |-
                                conditions is the current Condition of persistent volume claim. If underlying persistent volume is being
                                resized then the Condition will be set to 'Resizing'.
                              items:
                                description: PersistentVolumeClaimCondition contains
                                  details about state of pvc
                                properties:
                                  lastProbeTime:
                                    description: lastProbeTime is the time we probed
                                      the condition.
                                    format: date-time
                                    type: string
                                  lastTransitionTime:
                                    description: lastTransitionTime is the time the
                                      condition transitioned from one status to another.
                                    format: date-time
                                    type: string
                                  message:
                                    description: message is the human-readable message
                                      indicating details about last transition.
                                    type: string
                                  reason:
                                    description: |-
                                      reason is a unique, this should be a short, machine understandable string that gives the reason
                                      for condition's last transition. If it reports "Resizing" that means the underlying
                                      persistent volume is being resized.
                                    type: string
                                  status:
                                    type: string
                                  type:
                                    description: PersistentVolumeClaimConditionType
                                      is a valid value of PersistentVolumeClaimCondition.Type
                                    type: string
                                required:
                                - status
                                - type
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - type
                              x-kubernetes-list-type: map
                            currentVolumeAttributesClassName:
                              description: |-
                                currentVolumeAttributesClassName is the current name of the VolumeAttributesClass the PVC is using.
                                When unset, there is no VolumeAttributeClass applied to this PersistentVolumeClaim
                                This is an alpha field and requires enabling VolumeAttributesClass feature.
                              type: string
                            modifyVolumeStatus:
                              description: |-
                                ModifyVolumeStatus represents the status object of ControllerModifyVolume operation.
                                When this is unset, there is no ModifyVolume operation being attempted.
                                This is an alpha field and requires enabling VolumeAttributesClass feature.
                              properties:
                                status:
                                  description: "status is the status of the ControllerModifyVolume
                                    operation. It can be in any of following states:\n
                                    - Pending\n   Pending indicates that the PersistentVolumeClaim
                                    cannot be modified due to unmet requirements,
                                    such as\n   the specified VolumeAttributesClass
                                    not existing.\n - InProgress\n   InProgress indicates
                                    that the volume is being modified.\n - Infeasible\n
                                    \ Infeasible indicates that the request has been
                                    rejected as invalid by the CSI driver. To\n\t
                                    \ resolve the error, a valid VolumeAttributesClass
                                    needs to be specified.\nNote: New statuses can
                                    be added in the future. Consumers should check
                                    for unknown statuses and fail appropriately."
                                  type: string
                                targetVolumeAttributesClassName:
                                  description: targetVolumeAttributesClassName is
                                    the name of the VolumeAttributesClass the PVC
                                    currently being reconciled
                                  type: string
                              required:
                              - status
                              type: object
                            phase:
                              description: phase represents the current phase of PersistentVolumeClaim.
                              type: string
                          type: object
                      type: object
                    type: array
                type: object
                x-kubernetes-preserve-unknown-fields: true
              lifecycle:
                description: Lifecycle contains the hooks for Pod lifecycle.
                properties:
                  inPlaceUpdate:
                    description: InPlaceUpdate is the hook before Pod to update and
                      after Pod has been updated.
                    properties:
                      finalizersHandler:
                        items:
                          type: string
                        type: array
                      labelsHandler:
                        additionalProperties:
                          type: string
                        type: object
                      markPodNotReady:
                        description: |-
                          MarkPodNotReady = true means:
                          - Pod will be set to 'NotReady' at preparingDelete/preparingUpdate state.
                          - Pod will be restored to 'Ready' at Updated state if it was set to 'NotReady' at preparingUpdate state.
                          Currently, MarkPodNotReady only takes effect on InPlaceUpdate & PreDelete hook.
                          Default to false.
                        type: boolean
                    type: object
                  preDelete:
                    description: PreDelete is the hook before Pod to be deleted.
                    properties:
                      finalizersHandler:
                        items:
                          type: string
                        type: array
                      labelsHandler:
                        additionalProperties:
                          type: string
                        type: object
                      markPodNotReady:
                        description: |-
                          MarkPodNotReady = true means:
                          - Pod will be set to 'NotReady' at preparingDelete/preparingUpdate state.
                          - Pod will be restored to 'Ready' at Updated state if it was set to 'NotReady' at preparingUpdate state.
                          Currently, MarkPodNotReady only takes effect on InPlaceUpdate & PreDelete hook.
                          Default to false.
                        type: boolean
                    type: object
                  preNormal:
                    description: PreNormal is the hook after Pod to be created and
                      ready to be Normal.
                    properties:
                      finalizersHandler:
                        items:
                          type: string
                        type: array
                      labelsHandler:
                        additionalProperties:
                          type: string
                        type: object
                      markPodNotReady:
                        description: |-
                          MarkPodNotReady = true means:
                          - Pod will be set to 'NotReady' at preparingDelete/preparingUpdate state.
                          - Pod will be restored to 'Ready' at Updated state if it was set to 'NotReady' at preparingUpdate state.
                          Currently, MarkPodNotReady only takes effect on InPlaceUpdate & PreDelete hook.
                          Default to false.
                        type: boolean
                    type: object
                type: object
              network:
                properties:
                  networkConf:
                    items:
                      properties:
                        name:
                          type: string
                        value:
                          type: string
                      type: object
                    type: array
                  networkType:
                    type: string
                type: object
              persistentVolumeClaimRetentionPolicy:
                description: |-
                  PersistentVolumeClaimRetentionPolicy describes the policy used for PVCs created from
                  the StatefulSet VolumeClaimTemplates. This requires the
                  StatefulSetAutoDeletePVC feature gate to be enabled, which is alpha.
                properties:
                  whenDeleted:
                    description: |-
                      WhenDeleted specifies what happens to PVCs created from StatefulSet
                      VolumeClaimTemplates when the StatefulSet is deleted. The default policy
                      of `Retain` causes PVCs to not be affected by StatefulSet deletion. The
                      `Delete` policy causes those PVCs to be deleted.
                    type: string
                  whenScaled:
                    description: |-
                      WhenScaled specifies what happens to PVCs created from StatefulSet
                      VolumeClaimTemplates when the StatefulSet is scaled down. The default
                      policy of `Retain` causes PVCs to not be affected by a scaledown. The
                      `Delete` policy causes the associated PVCs for any excess pods above
                      the replica count to be deleted.
                    type: string
                type: object
              replicas:
                description: |-
                  replicas is the desired number of replicas of the given Template.
                  These are replicas in the sense that they are instantiations of the
                  same Template, but individual replicas also have a consistent identity.
                format: int32
                minimum: 0
                type: integer
              reserveGameServerIds:
                items:
                  anyOf:
                  - type: integer
                  - type: string
                  x-kubernetes-int-or-string: true
                type: array
              scaleStrategy:
                properties:
                  maxUnavailable:
                    anyOf:
                    - type: integer
                    - type: string
                    description: |-
                      The maximum number of pods that can be unavailable during scaling.
                      Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
                      Absolute number is calculated from percentage by rounding down.
                      It can just be allowed to work with Parallel podManagementPolicy.
                    x-kubernetes-int-or-string: true
                  scaleDownStrategyType:
                    description: |-
                      ScaleDownStrategyType indicates the scaling down strategy.
                      Default is GeneralScaleDownStrategyType
                    type: string
                type: object
              serviceName:
                type: string
              serviceQualities:
                items:
                  properties:
                    containerName:
                      type: string
                    exec:
                      description: Exec specifies the action to take.
                      properties:
                        command:
                          description: |-
                            Command is the command line to execute inside the container, the working directory for the
                            command  is root ('/') in the container's filesystem. The command is simply exec'd, it is
                            not run inside a shell, so traditional shell instructions ('|', etc) won't work. To use
                            a shell, you need to explicitly call out to that shell.
                            Exit status of 0 is treated as live/healthy and non-zero is unhealthy.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    failureThreshold:
                      description: |-
                        Minimum consecutive failures for the probe to be considered failed after having succeeded.
                        Defaults to 3. Minimum value is 1.
                      format: int32
                      type: integer
                    grpc:
                      description: GRPC specifies an action involving a GRPC port.
                      properties:
                        port:
                          description: Port number of the gRPC service. Number must
                            be in the range 1 to 65535.
                          format: int32
                          type: integer
                        service:
                          default: ""
                          description: |-
                            Service is the name of the service to place in the gRPC HealthCheckRequest
                            (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).

                            If this is not specified, the default behavior is defined by gRPC.
                          type: string
                      required:
                      - port
                      type: object
                    httpGet:
                      description: HTTPGet specifies the http request to perform.
                      properties:
                        host:
                          description: |-
                            Host name to connect to, defaults to the pod IP. You probably want to set
                            "Host" in httpHeaders instead.
                          type: string
                        httpHeaders:
                          description: Custom headers to set in the request. HTTP
                            allows repeated headers.
                          items:
                            description: HTTPHeader describes a custom header to be
                              used in HTTP probes
                            properties:
                              name:
                                description: |-
                                  The header field name.
                                  This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                type: string
                              value:
                                description: The header field value
                                type: string
                            required:
                            - name
                            - value
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        path:
                          description: Path to access on the HTTP server.
                          type: string
                        port:
                          anyOf:
                          - type: integer
                          - type: string
                          description: |-
                            Name or number of the port to access on the container.
                            Number must be in the range 1 to 65535.
                            Name must be an IANA_SVC_NAME.
                          x-kubernetes-int-or-string: true
                        scheme:
                          description: |-
                            Scheme to use for connecting to the host.
                            Defaults to HTTP.
                          type: string
                      required:
                      - port
                      type: object
                    initialDelaySeconds:
                      description: |-
                        Number of seconds after the container has started before liveness probes are initiated.
                        More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                      format: int32
                      type: integer
                    name:
                      type: string
                    periodSeconds:
                      description: |-
                        How often (in seconds) to perform the probe.
                        Default to 10 seconds. Minimum value is 1.
                      format: int32
                      type: integer
                    permanent:
                      description: |-
                        Whether to make GameServerSpec not change after the ServiceQualityAction is executed.
                        When Permanent is true, regardless of the detection results, ServiceQualityAction will only be executed once.
                        When Permanent is false, ServiceQualityAction can be executed again even though ServiceQualityAction has been executed.
                      type: boolean
                    serviceQualityAction:
                      items:
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            type: object
                          containers:
                            description: |-
                              Containers can be used to make the corresponding GameServer container fields
                              different from the fields defined by GameServerTemplate in GameServerSetSpec.
                            items:
                              properties:
                                image:
                                  description: |-
                                    Image indicates the image of the container to update.
                                    When Image updated, pod.spec.containers[*].image will be updated immediately.
                                  type: string
                                name:
                                  description: Name indicates the name of the container
                                    to update.
                                  type: string
                                resources:
                                  description: |-
                                    Resources indicates the resources of the container to update.
                                    When Resources updated, pod.spec.containers[*].Resources will be not updated immediately,
                                    which will be updated when pod recreate.
                                  properties:
                                    claims:
                                      description: |-
                                        Claims lists the names of resources, defined in spec.resourceClaims,
                                        that are used by this container.

                                        This is an alpha field and requires enabling the
                                        DynamicResourceAllocation feature gate.

                                        This field is immutable. It can only be set for containers.
                                      items:
                                        description: ResourceClaim references one
                                          entry in PodSpec.ResourceClaims.
                                        properties:
                                          name:
                                            description: |-
                                              Name must match the name of one entry in pod.spec.resourceClaims of
                                              the Pod where this field is used. It makes that resource available
                                              inside a container.
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      description: |-
                                        Limits describes the maximum amount of compute resources allowed.
                                        More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      description: |-
                                        Requests describes the minimum amount of compute resources required.
                                        If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                        otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                        More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                      type: object
                                  type: object
                              required:
                              - name
                              type: object
                            type: array
                          deletionPriority:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                          labels:
                            additionalProperties:
                              type: string
                            type: object
                          networkDisabled:
                            type: boolean
                          opsState:
                            type: string
                          result:
                            description: |-
                              Result indicate the probe message returned by the script.
                              When Result is defined, it would exec action only when the according Result is actually returns.
                            type: string
                          state:
                            type: boolean
                          updatePriority:
                            anyOf:
                            - type: integer
                            - type: string
                            x-kubernetes-int-or-string: true
                        required:
                        - state
                        type: object
                      type: array
                    successThreshold:
                      description: |-
                        Minimum consecutive successes for the probe to be considered successful after having failed.
                        Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                      format: int32
                      type: integer
                    tcpSocket:
                      description: TCPSocket specifies an action involving a TCP port.
                      properties:
                        host:
                          description: 'Optional: Host name to connect to, defaults
                            to the pod IP.'
                          type: string
                        port:
                          anyOf:
                          - type: integer
                          - type: string
                          description: |-
                            Number or name of the port to access on the container.
                            Number must be in the range 1 to 65535.
                            Name must be an IANA_SVC_NAME.
                          x-kubernetes-int-or-string: true
                      required:
                      - port
                      type: object
                    terminationGracePeriodSeconds:
                      description: |-
                        Optional duration in seconds the pod needs to terminate gracefully upon probe failure.
                        The grace period is the duration in seconds after the processes running in the pod are sent
                        a termination signal and the time when the processes are forcibly halted with a kill signal.
                        Set this value longer than the expected cleanup time for your process.
                        If this value is nil, the pod's terminationGracePeriodSeconds will be used. Otherwise, this
                        value overrides the value provided by the pod spec.
                        Value must be non-negative integer. The value zero indicates stop immediately via
                        the kill signal (no opportunity to shut down).
                        This is a beta field and requires enabling ProbeTerminationGracePeriod feature gate.
                        Minimum value is 1. spec.terminationGracePeriodSeconds is used if unset.
                      format: int64
                      type: integer
                    timeoutSeconds:
                      description: |-
                        Number of seconds after which the probe times out.
                        Defaults to 1 second. Minimum value is 1.
                        More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                      format: int32
                      type: integer
                  required:
                  - name
                  - permanent
                  type: object
                type: array
              updateStrategy:
                properties:
                  rollingUpdate:
                    description: RollingUpdate is used to communicate parameters when
                      Type is RollingUpdateStatefulSetStrategyType.
                    properties:
                      inPlaceUpdateStrategy:
                        description: |-
                          UnorderedUpdate contains strategies for non-ordered update.
                          If it is not nil, pods will be updated with non-ordered sequence.
                          Noted that UnorderedUpdate can only be allowed to work with Parallel podManagementPolicy
                          UnorderedUpdate *kruiseV1beta1.UnorderedUpdateStrategy `json:"unorderedUpdate,omitempty"`
                          InPlaceUpdateStrategy contains strategies for in-place update.
                        properties:
                          gracePeriodSeconds:
                            description: |-
                              GracePeriodSeconds is the timespan between set Pod status to not-ready and update images in Pod spec
                              when in-place update a Pod.
                            format: int32
                            type: integer
                        type: object
                      maxUnavailable:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          The maximum number of pods that can be unavailable during the update.
                          Value can be an absolute number (ex: 5) or a percentage of desired pods (ex: 10%).
                          Absolute number is calculated from percentage by rounding down.
                          Also, maxUnavailable can just be allowed to work with Parallel podManagementPolicy.
                          Defaults to 1.
                        x-kubernetes-int-or-string: true
                      minReadySeconds:
                        description: |-
                          MinReadySeconds indicates how long will the pod be considered ready after it's updated.
                          MinReadySeconds works with both OrderedReady and Parallel podManagementPolicy.
                          It affects the pod scale up speed when the podManagementPolicy is set to be OrderedReady.
                          Combined with MaxUnavailable, it affects the pod update speed regardless of podManagementPolicy.
                          Default value is 0, max is 300.
                        format: int32
                        type: integer
                      partition:
                        description: |-
                          Partition indicates the ordinal at which the StatefulSet should be partitioned by default.
                          But if unorderedUpdate has been set:
                            - Partition indicates the number of pods with non-updated revisions when rolling update.
                            - It means controller will update $(replicas - partition) number of pod.
                          Default value is 0.
                        format: int32
                        type: integer
                      paused:
                        description: |-
                          Paused indicates that the StatefulSet is paused.
                          Default value is false
                        type: boolean
                      podUpdatePolicy:
                        description: |-
                          PodUpdatePolicy indicates how pods should be updated
                          Default value is "ReCreate"
                        type: string
                    type: object
                  type:
                    description: |-
                      Type indicates the type of the StatefulSetUpdateStrategy.
                      Default is RollingUpdate.
                    type: string
                type: object
            required:
            - replicas
            type: object
          status:
            description: GameServerSetStatus defines the observed state of GameServerSet
            properties:
              availableReplicas:
                format: int32
                type: integer
              currentReplicas:
                format: int32
                type: integer
              labelSelector:
                description: LabelSelector is label selectors for query over pods
                  that should match the replica count used by HPA.
                type: string
              maintainingReplicas:
                format: int32
                type: integer
              observedGeneration:
                description: The generation observed by the controller.
                format: int64
                type: integer
              preDeleteReplicas:
                format: int32
                type: integer
              readyReplicas:
                format: int32
                type: integer
              replicas:
                description: replicas from advancedStatefulSet
                format: int32
                type: integer
              updatedReadyReplicas:
                format: int32
                type: integer
              updatedReplicas:
                format: int32
                type: integer
              waitToBeDeletedReplicas:
                format: int32
                type: integer
            required:
            - availableReplicas
            - currentReplicas
            - readyReplicas
            - replicas
            - updatedReplicas
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.labelSelector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.replicas
      status: {}
