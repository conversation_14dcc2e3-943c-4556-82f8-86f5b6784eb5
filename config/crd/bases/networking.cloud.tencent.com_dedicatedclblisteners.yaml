---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: dedicatedclblisteners.networking.cloud.tencent.com
spec:
  group: networking.cloud.tencent.com
  names:
    kind: DedicatedCLBListener
    listKind: DedicatedCLBListenerList
    plural: dedicatedclblisteners
    singular: dedicatedclblistener
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: CLB ID
      jsonPath: .spec.lbId
      name: LbId
      type: string
    - description: Port of CLB Listener
      jsonPath: .spec.lbPort
      name: LbPort
      type: integer
    - description: Pod name of target pod
      jsonPath: .spec.targetPod.podName
      name: Pod
      type: string
    - description: State of the dedicated clb listener
      jsonPath: .status.state
      name: State
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: DedicatedCLBListener is the Schema for the dedicatedclblisteners
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: DedicatedCLBListenerSpec defines the desired state of DedicatedCLBListener
            properties:
              extensiveParameters:
                type: string
              lbId:
                type: string
                x-kubernetes-validations:
                - message: Value is immutable
                  rule: self == oldSelf
              lbPort:
                format: int64
                type: integer
                x-kubernetes-validations:
                - message: Value is immutable
                  rule: self == oldSelf
              lbRegion:
                type: string
                x-kubernetes-validations:
                - message: Value is immutable
                  rule: self == oldSelf
              protocol:
                enum:
                - TCP
                - UDP
                type: string
                x-kubernetes-validations:
                - message: Value is immutable
                  rule: self == oldSelf
              targetPod:
                properties:
                  podName:
                    type: string
                  targetPort:
                    format: int64
                    type: integer
                required:
                - podName
                - targetPort
                type: object
            required:
            - lbId
            - lbPort
            - protocol
            type: object
          status:
            description: DedicatedCLBListenerStatus defines the observed state of
              DedicatedCLBListener
            properties:
              address:
                type: string
              listenerId:
                type: string
              message:
                type: string
              state:
                enum:
                - Bound
                - Available
                - Pending
                - Failed
                - Deleting
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
