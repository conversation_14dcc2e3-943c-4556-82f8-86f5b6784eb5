---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: poddnats.alibabacloud.com
spec:
  group: alibabacloud.com
  names:
    kind: PodDNAT
    listKind: PodDNATList
    plural: poddnats
    singular: poddnat
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: PodDNAT is the Schema for the poddnats API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: PodDNATSpec defines the desired state of PodDNAT
            properties:
              eni:
                type: string
              entryId:
                type: string
              externalIP:
                type: string
              externalPort:
                type: string
              internalIP:
                type: string
              internalPort:
                type: string
              portMapping:
                items:
                  properties:
                    externalPort:
                      type: string
                    internalPort:
                      type: string
                  type: object
                type: array
              protocol:
                type: string
              tableId:
                type: string
              vswitch:
                type: string
              zoneID:
                type: string
            type: object
          status:
            description: PodDNATStatus defines the observed state of PodDNAT
            properties:
              created:
                description: created create status
                type: string
              entries:
                description: entries
                items:
                  description: Entry record for forwardEntry
                  properties:
                    externalIP:
                      type: string
                    externalPort:
                      type: string
                    forwardEntryId:
                      type: string
                    internalIP:
                      type: string
                    internalPort:
                      type: string
                    ipProtocol:
                      type: string
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
