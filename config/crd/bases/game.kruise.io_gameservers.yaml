---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: gameservers.game.kruise.io
spec:
  group: game.kruise.io
  names:
    kind: GameServer
    listKind: GameServerList
    plural: gameservers
    shortNames:
    - gs
    singular: gameserver
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The current state of GameServer
      jsonPath: .status.currentState
      name: STATE
      type: string
    - description: The operations state of GameServer
      jsonPath: .spec.opsState
      name: OPSSTATE
      type: string
    - description: The current deletionPriority of GameServer
      jsonPath: .status.deletionPriority
      name: DP
      type: string
    - description: The current updatePriority of GameServer
      jsonPath: .status.updatePriority
      name: UP
      type: string
    - description: The age of GameServer
      jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: GameServer is the Schema for the gameservers API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: GameServerSpec defines the desired state of GameServer
            properties:
              containers:
                description: |-
                  Containers can be used to make the corresponding GameServer container fields
                  different from the fields defined by GameServerTemplate in GameServerSetSpec.
                items:
                  properties:
                    image:
                      description: |-
                        Image indicates the image of the container to update.
                        When Image updated, pod.spec.containers[*].image will be updated immediately.
                      type: string
                    name:
                      description: Name indicates the name of the container to update.
                      type: string
                    resources:
                      description: |-
                        Resources indicates the resources of the container to update.
                        When Resources updated, pod.spec.containers[*].Resources will be not updated immediately,
                        which will be updated when pod recreate.
                      properties:
                        claims:
                          description: |-
                            Claims lists the names of resources, defined in spec.resourceClaims,
                            that are used by this container.

                            This is an alpha field and requires enabling the
                            DynamicResourceAllocation feature gate.

                            This field is immutable. It can only be set for containers.
                          items:
                            description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                            properties:
                              name:
                                description: |-
                                  Name must match the name of one entry in pod.spec.resourceClaims of
                                  the Pod where this field is used. It makes that resource available
                                  inside a container.
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            Limits describes the maximum amount of compute resources allowed.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            Requests describes the minimum amount of compute resources required.
                            If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                            otherwise to an implementation-defined value. Requests cannot exceed Limits.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          type: object
                      type: object
                  required:
                  - name
                  type: object
                type: array
              deletionPriority:
                anyOf:
                - type: integer
                - type: string
                x-kubernetes-int-or-string: true
              networkDisabled:
                type: boolean
              opsState:
                type: string
              updatePriority:
                anyOf:
                - type: integer
                - type: string
                x-kubernetes-int-or-string: true
            type: object
          status:
            description: GameServerStatus defines the observed state of GameServer
            properties:
              conditions:
                description: Conditions is an array of current observed GameServer
                  conditions.
                items:
                  properties:
                    lastProbeTime:
                      description: Last time we probed the condition.
                      format: date-time
                      type: string
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    message:
                      description: Human-readable message indicating details about
                        last transition.
                      type: string
                    reason:
                      description: Unique, one-word, CamelCase reason for the condition's
                        last transition.
                      type: string
                    status:
                      description: |-
                        Status is the status of the condition.
                        Can be True, False, Unknown.
                      type: string
                    type:
                      description: Type is the type of the condition.
                      type: string
                  required:
                  - status
                  - type
                  type: object
                type: array
              currentState:
                type: string
              deletionPriority:
                anyOf:
                - type: integer
                - type: string
                x-kubernetes-int-or-string: true
              desiredState:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                type: string
              lastTransitionTime:
                format: date-time
                type: string
              networkStatus:
                properties:
                  createTime:
                    format: date-time
                    type: string
                  currentNetworkState:
                    type: string
                  desiredNetworkState:
                    type: string
                  externalAddresses:
                    items:
                      properties:
                        endPoint:
                          type: string
                        ip:
                          type: string
                        portRange:
                          properties:
                            portRange:
                              type: string
                            protocol:
                              description: Protocol defines network protocols supported
                                for things like container ports.
                              type: string
                          type: object
                        ports:
                          items:
                            properties:
                              name:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                              protocol:
                                description: Protocol defines network protocols supported
                                  for things like container ports.
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                      required:
                      - ip
                      type: object
                    type: array
                  internalAddresses:
                    items:
                      properties:
                        endPoint:
                          type: string
                        ip:
                          type: string
                        portRange:
                          properties:
                            portRange:
                              type: string
                            protocol:
                              description: Protocol defines network protocols supported
                                for things like container ports.
                              type: string
                          type: object
                        ports:
                          items:
                            properties:
                              name:
                                type: string
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                x-kubernetes-int-or-string: true
                              protocol:
                                description: Protocol defines network protocols supported
                                  for things like container ports.
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                      required:
                      - ip
                      type: object
                    type: array
                  lastTransitionTime:
                    format: date-time
                    type: string
                  networkType:
                    type: string
                type: object
              podStatus:
                description: |-
                  PodStatus represents information about the status of a pod. Status may trail the actual
                  state of a system, especially if the node that hosts the pod cannot contact the control
                  plane.
                properties:
                  conditions:
                    description: |-
                      Current service state of pod.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
                    items:
                      description: PodCondition contains details for the current condition
                        of this pod.
                      properties:
                        lastProbeTime:
                          description: Last time we probed the condition.
                          format: date-time
                          type: string
                        lastTransitionTime:
                          description: Last time the condition transitioned from one
                            status to another.
                          format: date-time
                          type: string
                        message:
                          description: Human-readable message indicating details about
                            last transition.
                          type: string
                        reason:
                          description: Unique, one-word, CamelCase reason for the
                            condition's last transition.
                          type: string
                        status:
                          description: |-
                            Status is the status of the condition.
                            Can be True, False, Unknown.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
                          type: string
                        type:
                          description: |-
                            Type is the type of the condition.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-conditions
                          type: string
                      required:
                      - status
                      - type
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - type
                    x-kubernetes-list-type: map
                  containerStatuses:
                    description: |-
                      The list has one entry per container in the manifest.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status
                    items:
                      description: ContainerStatus contains details for the current
                        status of this container.
                      properties:
                        allocatedResources:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            AllocatedResources represents the compute resources allocated for this container by the
                            node. Kubelet sets this value to Container.Resources.Requests upon successful pod admission
                            and after successfully admitting desired pod resize.
                          type: object
                        containerID:
                          description: |-
                            ContainerID is the ID of the container in the format '<type>://<container_id>'.
                            Where type is a container runtime identifier, returned from Version call of CRI API
                            (for example "containerd").
                          type: string
                        image:
                          description: |-
                            Image is the name of container image that the container is running.
                            The container image may not match the image used in the PodSpec,
                            as it may have been resolved by the runtime.
                            More info: https://kubernetes.io/docs/concepts/containers/images.
                          type: string
                        imageID:
                          description: |-
                            ImageID is the image ID of the container's image. The image ID may not
                            match the image ID of the image used in the PodSpec, as it may have been
                            resolved by the runtime.
                          type: string
                        lastState:
                          description: |-
                            LastTerminationState holds the last termination state of the container to
                            help debug container crashes and restarts. This field is not
                            populated if the container is still running and RestartCount is 0.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        name:
                          description: |-
                            Name is a DNS_LABEL representing the unique name of the container.
                            Each container in a pod must have a unique name across all container types.
                            Cannot be updated.
                          type: string
                        ready:
                          description: |-
                            Ready specifies whether the container is currently passing its readiness check.
                            The value will change as readiness probes keep executing. If no readiness
                            probes are specified, this field defaults to true once the container is
                            fully started (see Started field).

                            The value is typically used to determine whether a container is ready to
                            accept traffic.
                          type: boolean
                        resources:
                          description: |-
                            Resources represents the compute resource requests and limits that have been successfully
                            enacted on the running container after it has been started or has been successfully resized.
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                        restartCount:
                          description: |-
                            RestartCount holds the number of times the container has been restarted.
                            Kubelet makes an effort to always increment the value, but there
                            are cases when the state may be lost due to node restarts and then the value
                            may be reset to 0. The value is never negative.
                          format: int32
                          type: integer
                        started:
                          description: |-
                            Started indicates whether the container has finished its postStart lifecycle hook
                            and passed its startup probe.
                            Initialized as false, becomes true after startupProbe is considered
                            successful. Resets to false when the container is restarted, or if kubelet
                            loses state temporarily. In both cases, startup probes will run again.
                            Is always true when no startupProbe is defined and container is running and
                            has passed the postStart lifecycle hook. The null value must be treated the
                            same as false.
                          type: boolean
                        state:
                          description: State holds details about the container's current
                            condition.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        volumeMounts:
                          description: Status of volume mounts.
                          items:
                            description: VolumeMountStatus shows status of volume
                              mounts.
                            properties:
                              mountPath:
                                description: MountPath corresponds to the original
                                  VolumeMount.
                                type: string
                              name:
                                description: Name corresponds to the name of the original
                                  VolumeMount.
                                type: string
                              readOnly:
                                description: ReadOnly corresponds to the original
                                  VolumeMount.
                                type: boolean
                              recursiveReadOnly:
                                description: |-
                                  RecursiveReadOnly must be set to Disabled, Enabled, or unspecified (for non-readonly mounts).
                                  An IfPossible value in the original VolumeMount must be translated to Disabled or Enabled,
                                  depending on the mount result.
                                type: string
                            required:
                            - mountPath
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - mountPath
                          x-kubernetes-list-type: map
                      required:
                      - image
                      - imageID
                      - name
                      - ready
                      - restartCount
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  ephemeralContainerStatuses:
                    description: Status for any ephemeral containers that have run
                      in this pod.
                    items:
                      description: ContainerStatus contains details for the current
                        status of this container.
                      properties:
                        allocatedResources:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            AllocatedResources represents the compute resources allocated for this container by the
                            node. Kubelet sets this value to Container.Resources.Requests upon successful pod admission
                            and after successfully admitting desired pod resize.
                          type: object
                        containerID:
                          description: |-
                            ContainerID is the ID of the container in the format '<type>://<container_id>'.
                            Where type is a container runtime identifier, returned from Version call of CRI API
                            (for example "containerd").
                          type: string
                        image:
                          description: |-
                            Image is the name of container image that the container is running.
                            The container image may not match the image used in the PodSpec,
                            as it may have been resolved by the runtime.
                            More info: https://kubernetes.io/docs/concepts/containers/images.
                          type: string
                        imageID:
                          description: |-
                            ImageID is the image ID of the container's image. The image ID may not
                            match the image ID of the image used in the PodSpec, as it may have been
                            resolved by the runtime.
                          type: string
                        lastState:
                          description: |-
                            LastTerminationState holds the last termination state of the container to
                            help debug container crashes and restarts. This field is not
                            populated if the container is still running and RestartCount is 0.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        name:
                          description: |-
                            Name is a DNS_LABEL representing the unique name of the container.
                            Each container in a pod must have a unique name across all container types.
                            Cannot be updated.
                          type: string
                        ready:
                          description: |-
                            Ready specifies whether the container is currently passing its readiness check.
                            The value will change as readiness probes keep executing. If no readiness
                            probes are specified, this field defaults to true once the container is
                            fully started (see Started field).

                            The value is typically used to determine whether a container is ready to
                            accept traffic.
                          type: boolean
                        resources:
                          description: |-
                            Resources represents the compute resource requests and limits that have been successfully
                            enacted on the running container after it has been started or has been successfully resized.
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                        restartCount:
                          description: |-
                            RestartCount holds the number of times the container has been restarted.
                            Kubelet makes an effort to always increment the value, but there
                            are cases when the state may be lost due to node restarts and then the value
                            may be reset to 0. The value is never negative.
                          format: int32
                          type: integer
                        started:
                          description: |-
                            Started indicates whether the container has finished its postStart lifecycle hook
                            and passed its startup probe.
                            Initialized as false, becomes true after startupProbe is considered
                            successful. Resets to false when the container is restarted, or if kubelet
                            loses state temporarily. In both cases, startup probes will run again.
                            Is always true when no startupProbe is defined and container is running and
                            has passed the postStart lifecycle hook. The null value must be treated the
                            same as false.
                          type: boolean
                        state:
                          description: State holds details about the container's current
                            condition.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        volumeMounts:
                          description: Status of volume mounts.
                          items:
                            description: VolumeMountStatus shows status of volume
                              mounts.
                            properties:
                              mountPath:
                                description: MountPath corresponds to the original
                                  VolumeMount.
                                type: string
                              name:
                                description: Name corresponds to the name of the original
                                  VolumeMount.
                                type: string
                              readOnly:
                                description: ReadOnly corresponds to the original
                                  VolumeMount.
                                type: boolean
                              recursiveReadOnly:
                                description: |-
                                  RecursiveReadOnly must be set to Disabled, Enabled, or unspecified (for non-readonly mounts).
                                  An IfPossible value in the original VolumeMount must be translated to Disabled or Enabled,
                                  depending on the mount result.
                                type: string
                            required:
                            - mountPath
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - mountPath
                          x-kubernetes-list-type: map
                      required:
                      - image
                      - imageID
                      - name
                      - ready
                      - restartCount
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  hostIP:
                    description: |-
                      hostIP holds the IP address of the host to which the pod is assigned. Empty if the pod has not started yet.
                      A pod can be assigned to a node that has a problem in kubelet which in turns mean that HostIP will
                      not be updated even if there is a node is assigned to pod
                    type: string
                  hostIPs:
                    description: |-
                      hostIPs holds the IP addresses allocated to the host. If this field is specified, the first entry must
                      match the hostIP field. This list is empty if the pod has not started yet.
                      A pod can be assigned to a node that has a problem in kubelet which in turns means that HostIPs will
                      not be updated even if there is a node is assigned to this pod.
                    items:
                      description: HostIP represents a single IP address allocated
                        to the host.
                      properties:
                        ip:
                          description: IP is the IP address assigned to the host
                          type: string
                      required:
                      - ip
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  initContainerStatuses:
                    description: |-
                      The list has one entry per init container in the manifest. The most recent successful
                      init container will have ready = true, the most recently started container will have
                      startTime set.
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-and-container-status
                    items:
                      description: ContainerStatus contains details for the current
                        status of this container.
                      properties:
                        allocatedResources:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            AllocatedResources represents the compute resources allocated for this container by the
                            node. Kubelet sets this value to Container.Resources.Requests upon successful pod admission
                            and after successfully admitting desired pod resize.
                          type: object
                        containerID:
                          description: |-
                            ContainerID is the ID of the container in the format '<type>://<container_id>'.
                            Where type is a container runtime identifier, returned from Version call of CRI API
                            (for example "containerd").
                          type: string
                        image:
                          description: |-
                            Image is the name of container image that the container is running.
                            The container image may not match the image used in the PodSpec,
                            as it may have been resolved by the runtime.
                            More info: https://kubernetes.io/docs/concepts/containers/images.
                          type: string
                        imageID:
                          description: |-
                            ImageID is the image ID of the container's image. The image ID may not
                            match the image ID of the image used in the PodSpec, as it may have been
                            resolved by the runtime.
                          type: string
                        lastState:
                          description: |-
                            LastTerminationState holds the last termination state of the container to
                            help debug container crashes and restarts. This field is not
                            populated if the container is still running and RestartCount is 0.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        name:
                          description: |-
                            Name is a DNS_LABEL representing the unique name of the container.
                            Each container in a pod must have a unique name across all container types.
                            Cannot be updated.
                          type: string
                        ready:
                          description: |-
                            Ready specifies whether the container is currently passing its readiness check.
                            The value will change as readiness probes keep executing. If no readiness
                            probes are specified, this field defaults to true once the container is
                            fully started (see Started field).

                            The value is typically used to determine whether a container is ready to
                            accept traffic.
                          type: boolean
                        resources:
                          description: |-
                            Resources represents the compute resource requests and limits that have been successfully
                            enacted on the running container after it has been started or has been successfully resized.
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                        restartCount:
                          description: |-
                            RestartCount holds the number of times the container has been restarted.
                            Kubelet makes an effort to always increment the value, but there
                            are cases when the state may be lost due to node restarts and then the value
                            may be reset to 0. The value is never negative.
                          format: int32
                          type: integer
                        started:
                          description: |-
                            Started indicates whether the container has finished its postStart lifecycle hook
                            and passed its startup probe.
                            Initialized as false, becomes true after startupProbe is considered
                            successful. Resets to false when the container is restarted, or if kubelet
                            loses state temporarily. In both cases, startup probes will run again.
                            Is always true when no startupProbe is defined and container is running and
                            has passed the postStart lifecycle hook. The null value must be treated the
                            same as false.
                          type: boolean
                        state:
                          description: State holds details about the container's current
                            condition.
                          properties:
                            running:
                              description: Details about a running container
                              properties:
                                startedAt:
                                  description: Time at which the container was last
                                    (re-)started
                                  format: date-time
                                  type: string
                              type: object
                            terminated:
                              description: Details about a terminated container
                              properties:
                                containerID:
                                  description: Container's ID in the format '<type>://<container_id>'
                                  type: string
                                exitCode:
                                  description: Exit status from the last termination
                                    of the container
                                  format: int32
                                  type: integer
                                finishedAt:
                                  description: Time at which the container last terminated
                                  format: date-time
                                  type: string
                                message:
                                  description: Message regarding the last termination
                                    of the container
                                  type: string
                                reason:
                                  description: (brief) reason from the last termination
                                    of the container
                                  type: string
                                signal:
                                  description: Signal from the last termination of
                                    the container
                                  format: int32
                                  type: integer
                                startedAt:
                                  description: Time at which previous execution of
                                    the container started
                                  format: date-time
                                  type: string
                              required:
                              - exitCode
                              type: object
                            waiting:
                              description: Details about a waiting container
                              properties:
                                message:
                                  description: Message regarding why the container
                                    is not yet running.
                                  type: string
                                reason:
                                  description: (brief) reason the container is not
                                    yet running.
                                  type: string
                              type: object
                          type: object
                        volumeMounts:
                          description: Status of volume mounts.
                          items:
                            description: VolumeMountStatus shows status of volume
                              mounts.
                            properties:
                              mountPath:
                                description: MountPath corresponds to the original
                                  VolumeMount.
                                type: string
                              name:
                                description: Name corresponds to the name of the original
                                  VolumeMount.
                                type: string
                              readOnly:
                                description: ReadOnly corresponds to the original
                                  VolumeMount.
                                type: boolean
                              recursiveReadOnly:
                                description: |-
                                  RecursiveReadOnly must be set to Disabled, Enabled, or unspecified (for non-readonly mounts).
                                  An IfPossible value in the original VolumeMount must be translated to Disabled or Enabled,
                                  depending on the mount result.
                                type: string
                            required:
                            - mountPath
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - mountPath
                          x-kubernetes-list-type: map
                      required:
                      - image
                      - imageID
                      - name
                      - ready
                      - restartCount
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  message:
                    description: A human readable message indicating details about
                      why the pod is in this condition.
                    type: string
                  nominatedNodeName:
                    description: |-
                      nominatedNodeName is set only when this pod preempts other pods on the node, but it cannot be
                      scheduled right away as preemption victims receive their graceful termination periods.
                      This field does not guarantee that the pod will be scheduled on this node. Scheduler may decide
                      to place the pod elsewhere if other nodes become available sooner. Scheduler may also decide to
                      give the resources on this node to a higher priority pod that is created after preemption.
                      As a result, this field may be different than PodSpec.nodeName when the pod is
                      scheduled.
                    type: string
                  phase:
                    description: |-
                      The phase of a Pod is a simple, high-level summary of where the Pod is in its lifecycle.
                      The conditions array, the reason and message fields, and the individual container status
                      arrays contain more detail about the pod's status.
                      There are five possible phase values:

                      Pending: The pod has been accepted by the Kubernetes system, but one or more of the
                      container images has not been created. This includes time before being scheduled as
                      well as time spent downloading images over the network, which could take a while.
                      Running: The pod has been bound to a node, and all of the containers have been created.
                      At least one container is still running, or is in the process of starting or restarting.
                      Succeeded: All containers in the pod have terminated in success, and will not be restarted.
                      Failed: All containers in the pod have terminated, and at least one container has
                      terminated in failure. The container either exited with non-zero status or was terminated
                      by the system.
                      Unknown: For some reason the state of the pod could not be obtained, typically due to an
                      error in communicating with the host of the pod.

                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#pod-phase
                    type: string
                  podIP:
                    description: |-
                      podIP address allocated to the pod. Routable at least within the cluster.
                      Empty if not yet allocated.
                    type: string
                  podIPs:
                    description: |-
                      podIPs holds the IP addresses allocated to the pod. If this field is specified, the 0th entry must
                      match the podIP field. Pods may be allocated at most 1 value for each of IPv4 and IPv6. This list
                      is empty if no IPs have been allocated yet.
                    items:
                      description: PodIP represents a single IP address allocated
                        to the pod.
                      properties:
                        ip:
                          description: IP is the IP address assigned to the pod
                          type: string
                      required:
                      - ip
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - ip
                    x-kubernetes-list-type: map
                  qosClass:
                    description: |-
                      The Quality of Service (QOS) classification assigned to the pod based on resource requirements
                      See PodQOSClass type for available QOS classes
                      More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-qos/#quality-of-service-classes
                    type: string
                  reason:
                    description: |-
                      A brief CamelCase message indicating details about why the pod is in this state.
                      e.g. 'Evicted'
                    type: string
                  resize:
                    description: |-
                      Status of resources resize desired for pod's containers.
                      It is empty if no resources resize is pending.
                      Any changes to container resources will automatically set this to "Proposed"
                    type: string
                  resourceClaimStatuses:
                    description: Status of resource claims.
                    items:
                      description: |-
                        PodResourceClaimStatus is stored in the PodStatus for each PodResourceClaim
                        which references a ResourceClaimTemplate. It stores the generated name for
                        the corresponding ResourceClaim.
                      properties:
                        name:
                          description: |-
                            Name uniquely identifies this resource claim inside the pod.
                            This must match the name of an entry in pod.spec.resourceClaims,
                            which implies that the string must be a DNS_LABEL.
                          type: string
                        resourceClaimName:
                          description: |-
                            ResourceClaimName is the name of the ResourceClaim that was
                            generated for the Pod in the namespace of the Pod. It this is
                            unset, then generating a ResourceClaim was not necessary. The
                            pod.spec.resourceClaims entry can be ignored in this case.
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                  startTime:
                    description: |-
                      RFC 3339 date and time at which the object was acknowledged by the Kubelet.
                      This is before the Kubelet pulled the container image(s) for the pod.
                    format: date-time
                    type: string
                type: object
              serviceQualitiesConditions:
                items:
                  properties:
                    lastActionTransitionTime:
                      format: date-time
                      type: string
                    lastProbeTime:
                      format: date-time
                      type: string
                    lastTransitionTime:
                      format: date-time
                      type: string
                    name:
                      type: string
                    result:
                      description: Result indicate the probe message returned by the
                        script
                      type: string
                    status:
                      type: string
                  required:
                  - name
                  type: object
                type: array
              updatePriority:
                anyOf:
                - type: integer
                - type: string
                description: Lifecycle defines the lifecycle hooks for Pods pre-delete,
                  in-place update.
                x-kubernetes-int-or-string: true
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
