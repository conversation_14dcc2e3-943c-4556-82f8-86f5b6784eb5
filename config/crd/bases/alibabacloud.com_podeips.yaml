---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.5
  name: podeips.alibabacloud.com
spec:
  group: alibabacloud.com
  names:
    kind: PodEIP
    listKind: PodEIPList
    plural: podeips
    singular: podeip
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: PodEIP is the Schema for the podeips API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: PodEIPSpec defines the desired state of PodEIP
            properties:
              allocationID:
                type: string
              allocationType:
                description: AllocationType ip type and release strategy
                properties:
                  releaseAfter:
                    type: string
                  releaseStrategy:
                    allOf:
                    - enum:
                      - Follow
                      - TTL
                      - Never
                    - enum:
                      - Follow
                      - TTL
                      - Never
                    description: ReleaseStrategy is the type for eip release strategy
                    type: string
                  type:
                    default: Auto
                    description: IPAllocType is the type for eip alloc strategy
                    enum:
                    - Auto
                    - Static
                    type: string
                required:
                - releaseStrategy
                - type
                type: object
              bandwidthPackageID:
                type: string
            required:
            - allocationID
            - allocationType
            type: object
          status:
            description: PodEIPStatus defines the observed state of PodEIP
            properties:
              bandwidthPackageID:
                description: BandwidthPackageID
                type: string
              eipAddress:
                description: eip
                type: string
              internetChargeType:
                type: string
              isp:
                type: string
              name:
                type: string
              networkInterfaceID:
                description: eni
                type: string
              podLastSeen:
                description: PodLastSeen is the timestamp when pod resource last seen
                format: date-time
                type: string
              privateIPAddress:
                type: string
              publicIpAddressPoolID:
                type: string
              resourceGroupID:
                type: string
              status:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
