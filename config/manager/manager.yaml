apiVersion: v1
kind: Namespace
metadata:
  labels:
    control-plane: controller-manager
  name: system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
  labels:
    control-plane: controller-manager
spec:
  selector:
    matchLabels:
      control-plane: controller-manager
  replicas: 1
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: manager
      labels:
        control-plane: controller-manager
    spec:
#      securityContext:
#        runAsNonRoot: true
        # TODO(user): For common cases that do not require escalating privileges
        # it is recommended to ensure that all your Pods/Containers are restrictive.
        # More info: https://kubernetes.io/docs/concepts/security/pod-security-standards/#restricted
        # Please uncomment the following code if your project does NOT have to work on old Kubernetes
        # versions < 1.19 or on vendors versions which do NOT support this field by default (i.e. Openshift < 4.11 ).
        # seccompProfile:
        #   type: RuntimeDefault
      containers:
      - command:
        - /manager
        args:
        - --leader-elect=false
        - --provider-config=/etc/kruise-game/config.toml
        - --api-server-qps=5
        - --api-server-qps-burst=10
        - --enable-cert-generation=false
        image: controller:latest
        name: manager
        env:
          - name: "NETWORK_TOTAL_WAIT_TIME"
            value: "60"
          - name: "NETWORK_PROBE_INTERVAL_TIME"
            value: "5"
        ports:
          - name: https
            containerPort: 8080
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - "ALL"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8082
          initialDelaySeconds: 5
          periodSeconds: 5
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8082
          initialDelaySeconds: 5
          periodSeconds: 5
        # TODO(user): Configure the resources accordingly based on the project requirements.
        # More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
        resources:
          limits:
            cpu: 500m
            memory: 1024Mi
          requests:
            cpu: 10m
            memory: 64Mi
        volumeMounts:
          - mountPath: /etc/kruise-game
            name: provider-config
      serviceAccountName: controller-manager
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: config.toml
                path: config.toml
            name: kruise-game-manager-config
          name: provider-config

