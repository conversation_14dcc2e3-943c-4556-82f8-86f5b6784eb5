{"__inputs": [{"name": "DS_TEMP", "label": "temp", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.2.4"}, {"type": "panel", "id": "piechart", "name": "Pie chart", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 0, "y": 0}, "id": 20, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "sum(okg_gameservers_state_count)", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 3, "y": 0}, "id": 26, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_state_count{state=\"Ready\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "Ready GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 6, "y": 0}, "id": 22, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_opsState_count{opsState=\"Maintaining\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "Maintaining GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 9, "y": 0}, "id": 24, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_opsState_count{opsState=\"WaitToBeDeleted\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "WaitToBeDeleted GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.0.8", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameservers_state_count{state!=\"\"}) by (state)", "fields": [{"jsonPath": ""}], "legendFormat": "{{state}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer State (now)", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 10, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.0.8", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameservers_opsState_count) by (opsState)", "fields": [{"jsonPath": ""}], "legendFormat": "{{opsState}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer OpsState (now)", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 0, "y": 5}, "id": 32, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_state_count{state=\"Deleting\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "Deleting GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 3, "y": 5}, "id": 28, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_state_count{state=\"Updating\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "Updating GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 6, "y": 5}, "id": 34, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_state_count{state=\"NotReady\"})", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "NotReady GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 3, "x": 9, "y": 5}, "id": 30, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "expr": "max(okg_gameservers_total)", "fields": [{"jsonPath": ""}], "method": "GET", "queryParams": "", "refId": "A", "urlPath": ""}], "title": "Existed GameServers", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "9.0.8", "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "exemplar": false, "expr": "max(okg_gameservers_state_count{state!=\"\"}) by (state)", "fields": [{"jsonPath": ""}], "instant": false, "interval": "", "legendFormat": "{{state}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer State (Time series)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameservers_opsState_count) by (opsState)", "fields": [{"jsonPath": ""}], "legendFormat": "{{opsState}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer OpsState (Time series)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameserver_deletion_priority{gsNs=~\"$namespace\",gsName=~\"$gsName\"}) by (gsNs,gsName)", "fields": [{"jsonPath": ""}], "legendFormat": "{{gsNs}}/{{gsName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer Deletion Priority", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameserver_update_priority{gsNs=~\"$namespace\",gsName=~\"$gsName\"}) by (gsNs,gsName)", "fields": [{"jsonPath": ""}], "legendFormat": "{{gsNs}}/{{gsName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "GameServer Update Priority", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "exemplar": false, "expr": "max(okg_gameserversets_replicas_count{gsStatus=\"current\",gssNs=~\"$namespace\",gssName=~\"$gssName\"}) by (gssNs,gssName)", "fields": [{"jsonPath": ""}], "instant": false, "interval": "", "legendFormat": "{{gssNs}}/{{gssName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "The Number of Current Gs in GameServerSet", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameserversets_replicas_count{gsStatus=\"available\",gssNs=~\"$namespace\",gssName=~\"$gssName\"}) by (gssNs,gssName)", "fields": [{"jsonPath": ""}], "legendFormat": "{{gssNs}}/{{gssName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "The Number of Available Gs in GameServerSet", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 34}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameserversets_replicas_count{gsStatus=\"maintaining\",gssNs=~\"$namespace\",gssName=~\"$gssName\"}) by (gssNs,gssName)", "fields": [{"jsonPath": ""}], "legendFormat": "{{gssNs}}/{{gssName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "The Number of Maintaining Gs in GameServerSet", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 34}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"cacheDurationSeconds": 300, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "editorMode": "code", "expr": "max(okg_gameserversets_replicas_count{gsStatus=\"waitToBeDeleted\",gssNs=~\"$namespace\",gssName=~\"$gssName\"}) by (gssNs,gssName)", "fields": [{"jsonPath": ""}], "legendFormat": "{{gssNs}}/{{gssName}}", "method": "GET", "queryParams": "", "range": true, "refId": "A", "urlPath": ""}], "title": "The Number of WaitToBeDeleted Gs in GameServerSet", "type": "timeseries"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": "", "current": {}, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "definition": "label_values(okg_gameserversets_replicas_count, gssNs)", "hide": 0, "includeAll": true, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(okg_gameserversets_replicas_count, gssNs)", "refId": "temp-namespace-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "", "current": {}, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "definition": "label_values(okg_gameserversets_replicas_count, gssName) ", "hide": 0, "includeAll": true, "multi": false, "name": "gssName", "options": [], "query": {"query": "label_values(okg_gameserversets_replicas_count, gssName) ", "refId": "temp-gssName-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_TEMP}"}, "definition": "label_values(okg_gameserver_deletion_priority, gsName) ", "hide": 0, "includeAll": true, "multi": false, "name": "gsName", "options": [], "query": {"query": "label_values(okg_gameserver_deletion_priority, gsName) ", "refId": "temp-gsName-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "OKG Metrics Statics", "version": 1, "weekStart": ""}