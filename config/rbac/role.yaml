---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - ""
  resources:
  - nodes
  - persistentvolumeclaims
  - persistentvolumes
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - nodes/status
  - persistentvolumeclaims/status
  - persistentvolumes/status
  verbs:
  - get
- apiGroups:
  - ""
  resources:
  - pods
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods/status
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - mutatingwebhookconfigurations
  - validatingwebhookconfigurations
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - alibabacloud.com
  resources:
  - poddnats
  - podeips
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - alibabacloud.com
  resources:
  - poddnats/status
  - podeips/status
  verbs:
  - get
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps.kruise.io
  resources:
  - podprobemarkers
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps.kruise.io
  resources:
  - statefulsets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - elbv2.k8s.aws
  resources:
  - targetgroupbindings
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - elbv2.services.k8s.aws
  resources:
  - listeners
  - targetgroups
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - game.kruise.io
  resources:
  - gameservers
  - gameserversets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - game.kruise.io
  resources:
  - gameservers/finalizers
  - gameserversets/finalizers
  verbs:
  - update
- apiGroups:
  - game.kruise.io
  resources:
  - gameservers/status
  - gameserversets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.cloud.tencent.com
  resources:
  - dedicatedclblisteners
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.cloud.tencent.com
  resources:
  - dedicatedclblisteners/status
  verbs:
  - get
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status
  verbs:
  - get
  - patch
  - update
