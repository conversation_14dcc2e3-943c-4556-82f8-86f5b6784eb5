apiVersion: apps/v1
kind: Deployment
metadata:
  name: controller-manager
  namespace: system
spec:
  template:
    spec:
      containers:
      - name: manager
        ports:
        - containerPort: 9876
          name: webhook-server
          protocol: TCP
        volumeMounts:
        - mountPath: /tmp/webhook-certs/
          name: cert
          readOnly: true
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName:  kruise-game-certs
          optional: false
