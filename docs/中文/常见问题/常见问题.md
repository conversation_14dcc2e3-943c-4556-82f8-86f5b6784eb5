## FAQ
1. 当不小心删除GameServer的时候会触发游戏服的删除吗？
   不会，GameServer只是游戏服的差异性运维动作的状态记录，如果删除GameServer之后，会重新创建一个使用默认配置的GameServer对象。此时，你的GameServer也会重置为默认定义在GameServerSet中的游戏服模板配置。

2. 如何让匹配服务与自动伸缩更好的配合防止出现玩家被强制下线？
   可以通过服务质量能力，将游戏的玩家任务转换为GameServer的状态，匹配框架感知GameServer的状态并控制伸缩的副本数目，GameServerSet也会根据GameServer的状态来判断删除的顺序，从而实现优雅下线。

3. MMORPG类型的游戏如何使用OpenKruiseGame？
   可以配合kubevela来进行复杂场景的游戏服编排，单个角色服使用GameServerSet进行管理。