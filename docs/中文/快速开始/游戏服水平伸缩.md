## OpenKruiseGame的伸缩特性

### 缩容顺序

OKG提供游戏服状态设置的能力，您可以手动/自动(服务质量功能)地设置游戏服的运维状态或删除优先级。当缩容时，GameServerSet负载会根据游戏服的状态进行缩容选择，缩容规则如下：

1）根据游戏服的opsState缩容。按顺序依次缩容opsState为`WaitToBeDeleted`、`None`、`Allocated`、`Maintaining`的游戏服

2）当opsState相同时，按照DeletionPriority(删除优先级)缩容，优先删除DeletionPriority大的游戏服

3）当opsState与DeletionPriority都相同时，优先删除名称尾部序号较大的游戏服

#### 示例

部署一个副本为5的游戏服：

```bash
cat <<EOF | kubectl apply -f -
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: minecraft
  namespace: default
spec:
  replicas: 5
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/acs/minecraft-demo:1.12.2
          name: minecraft
EOF
```

生成5个GameServer：

```bash
kubectl get gs
NAME          STATE   OPSSTATE   DP    UP
minecraft-0   Ready   None       0     0
minecraft-1   Ready   None       0     0
minecraft-2   Ready   None       0     0
minecraft-3   Ready   None       0     0
minecraft-4   Ready   None       0     0
```

对minecraft-2设置删除优先级为10：

```bash
kubectl edit gs minecraft-2

...
spec:
  deletionPriority: 10 #初始为0，调大到10
  opsState: None
  updatePriority: 0
...
```

手动缩容到4个副本：

```bash
kubectl scale gss minecraft --replicas=4
gameserverset.game.kruise.io/minecraft scale
```

游戏服的数目最终变为4，可以看到2号游戏服因为删除优先级最大所以被删除：

```bash
kubectl get gs
NAME          STATE      OPSSTATE   DP    UP
minecraft-0   Ready      None       0     0
minecraft-1   Ready      None       0     0
minecraft-2   Deleting   None       10    0
minecraft-3   Ready      None       0     0
minecraft-4   Ready      None       0     0

# After a while
...

kubectl get gs
NAME          STATE   OPSSTATE   DP    UP
minecraft-0   Ready   None       0     0
minecraft-1   Ready   None       0     0
minecraft-3   Ready   None       0     0
minecraft-4   Ready   None       0     0
```

设置minecraft-3的opsState为WaitToBeDeleted：

```bash
kubectl edit gs minecraft-3

...
spec:
  deletionPriority: 0 
  opsState: WaitToBeDeleted #初始为None, 将其改为WaitToBeDeleted
  updatePriority: 0
...
```

手动缩容到3个副本：

```bash
kubectl scale gss minecraft --replicas=3
gameserverset.game.kruise.io/minecraft scaled
```

游戏服的数目最终变为3，可以看到3号游戏服因为处于WaitToBeDeleted状态所以被删除：

```bash
kubectl get gs
NAME          STATE      OPSSTATE          DP    UP
minecraft-0   Ready      None              0     0
minecraft-1   Ready      None              0     0
minecraft-3   Deleting   WaitToBeDeleted   0     0
minecraft-4   Ready      None              0     0

# After a while
...

kubectl get gs
NAME          STATE   OPSSTATE   DP    UP
minecraft-0   Ready   None       0     0
minecraft-1   Ready   None       0     0
minecraft-4   Ready   None       0     0
```

手动扩容回5个副本：

```bash
kubectl scale gss minecraft --replicas=5
gameserverset.game.kruise.io/minecraft scaled
```

游戏服的数目最终变为5，此时扩容出的游戏服序号为2与3：

```bash
kubectl get gs
NAME          STATE   OPSSTATE   DP    UP
minecraft-0   Ready   None       0     0
minecraft-1   Ready   None       0     0
minecraft-2   Ready   None       0     0
minecraft-3   Ready   None       0     0
minecraft-4   Ready   None       0     0
```

### 游戏服 ID Reserve

GameServerSet提供了`Spec.ReserveGameServerIds`字段。通过该字段，用户指定ID，将对应的游戏服删除；或者在创建新游戏服时避免该序号对应的游戏服生成。

例如，gss下存在5个游戏服，ID分别为0、1、2、3、4。此时设置`ReserveGameServerIds`，填写3和4，在不更改副本数目的情况下，gss将会删除3和4，同时生成5和6的游戏服，如下所示：

```bash
kubectl edit gss minecraft
...
spec:
  reserveGameServerIds:
  - 3
  - 4
...

# After a while
kubectl get gs
NAME          STATE      OPSSTATE   DP    UP    AGE
minecraft-0   Ready      None       0     0     79s
minecraft-1   Ready      None       0     0     79s
minecraft-2   Ready      None       0     0     79s
minecraft-3   Deleting   None       0     0     78s
minecraft-4   Deleting   None       0     0     78s
minecraft-5   Ready      None       0     0     23s
minecraft-6   Ready      None       0     0     23s
```

如若填写在`ReserveGameServerIds`字段增加5和6，同时减少副本数目到3，则gss会删除5和6的游戏服，如下所示：

```bash
kubectl edit gss minecraft
...
spec:
  replicas: 3
  reserveGameServerIds:
  - 3
  - 4
  - 5
  - 6
...

# After a while
kubectl get gs
NAME          STATE      OPSSTATE   DP    UP    AGE
minecraft-0   Ready      None       0     0     10m
minecraft-1   Ready      None       0     0     10m
minecraft-2   Ready      None       0     0     10m
minecraft-5   Deleting   None       0     0     9m55s
minecraft-6   Deleting   None       0     0     9m55s
```

**在缩容时，OKG将优先考虑被Reserve的游戏服，再按照上文提到的缩容顺序进行缩容**

### 缩容策略

OKG 提供两种缩容策略：1）General；2）ReserveIds。您可在`GameServerSet.Spec.ScaleStrategy.ScaleDownStrategyType`设置对应策略

#### General

当用户不配置ScaleDownStrategyType字段，General为默认配置。缩容行为如上文中所述。

#### ReserveIds

用户设置ScaleDownStrategyType为`ReserveIds`，当游戏服集合发生缩容时，被删掉的游戏服尾部序号会被记录在reserveGameServerIds中，后续发生扩容时，这些尾部序号不会再使用；如果想再使用这些尾部序号，只需要将它们从reserveGameServerIds中拿出来同时调整副本数即可。

##### 示例

例如，gss下存在5个游戏服，ID分别为0、1、2、3、4。此时设置`GameServerSet.Spec.ScaleStrategy.ScaleDownStrategyType`为`ReserveIds`，同时减少副本数目到3

```bash
kubectl edit gss minecraft
...
spec:
  replicas: 3
  scaleStrategy:
    scaleDownStrategyType: ReserveIds
...

# After a while
kubectl get gs
NAME          STATE      OPSSTATE   DP    UP    AGE
minecraft-0   Ready      None       0     0     10m
minecraft-1   Ready      None       0     0     10m
minecraft-2   Ready      None       0     0     10m
minecraft-3   Deleting   None       0     0     9m55s
minecraft-4   Deleting   None       0     0     9m55s
...

kubectl get gss minecraft -o yaml
spec:
  replicas: 3
  reserveGameServerIds:
  - 3
  - 4
  scaleStrategy:
  scaleDownStrategyType: ReserveIds
```

可以看到，序号为3和4的游戏服被回填到了`reserveGameServerIds`字段，此时若希望指定4号游戏服扩容，则将4从reserveGameServerIds去除，并增加副本数到4：

```bash
kubectl edit gss minecraft
...
spec:
  replicas: 4
  reserveGameServerIds:
  - 3
  scaleStrategy:
    scaleDownStrategyType: ReserveIds
...

# After a while

kubectl get gs
NAME          STATE   OPSSTATE   DP    UP    AGE
minecraft-0   Ready   None       0     0     17m
minecraft-1   Ready   None       0     0     17m
minecraft-2   Ready   None       0     0     17m
minecraft-4   Ready   None       0     0     6s
```

通过该功能可以实现指定序号游戏服扩容。

## 配置游戏服的自动伸缩

GameServerSet支持HPA，您可以通过默认/自定义指标配置

### HPA示例

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: minecraft-hpa
spec:
  scaleTargetRef:
    apiVersion: game.kruise.io/v1alpha1
    kind: GameServerSet
    name: minecraft # GameServerSet对应名称
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50 # 示例以cpu利用率50%为计算标准
```