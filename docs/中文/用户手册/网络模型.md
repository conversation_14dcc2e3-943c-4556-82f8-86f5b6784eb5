## 功能概述

如[OKG设计理念](../核心概念/OpenKruiseGame的设计理念.md)中提到的，游戏服接入层网络是游戏开发者非常关注的问题。
非网关架构下，游戏开发者需要考虑如何暴露游戏服的外部IP端口，供玩家连接访问。
在不同场景下，往往需要不同的网络产品，而有时网络产品由云厂商提供。OKG 的 Cloud Provider & Network Plugin 源于此而诞生。
OKG 会集成不同云提供商的不同网络插件，用户可通过GameServerSet设置游戏服的网络参数，并在生成的GameServer中查看网络状态信息，极大降低了游戏服接入网络的复杂度。

## 使用示例

### Kubernetes-HostPort

OKG支持在原生Kubernetes集群使用HostPort游戏服网络，使用游戏服所在宿主机暴露外部IP及端口，转发至游戏服内部端口中。使用方式如下。

部署一个带有network的GameServerSet：

```
cat <<EOF | kubectl apply -f -
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gs-hostport
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: Kubernetes-HostPort
    networkConf:
    #网络配置以k-v键值对的形式传入，由网络插件指定。不同网络插件有着不同的网络配置
    - name: ContainerPorts
      #ContainerPorts对应的值格式如下{containerName}:{port1}/{protocol1},{port2}/{protocol2},...
      value: "gameserver:80"
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/gs-demo/gameserver:network
          name: gameserver
EOF
```

生成的GameServer中通过networkStatus字段查看游戏服网络信息：

```shell
  networkStatus:
    createTime: "2022-11-23T10:57:01Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: **********
      ports:
      - name: gameserver-80
        port: 8211
        protocol: TCP
    internalAddresses:
    - ip: **********
      ports:
      - name: gameserver-80
        port: 80
        protocol: TCP
    lastTransitionTime: "2022-11-23T10:57:01Z"
    networkType: Kubernetes-HostPort
```

访问 **********:8211 即可

### AlibabaCloud-NATGW

OKG支持阿里云下NAT网关模型，使用NATGW的外部IP与端口暴露服务，流量最终将转发至Pod之中。使用方式如下：

```shell
cat <<EOF | kubectl apply -f -
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gs-natgw
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: AlibabaCloud-NATGW
    networkConf:
    - name: Ports
      #暴露的端口，格式如下 {port1},{port2}...
      value: "80"
    - name: Protocol
      #使用的协议，默认为TCP
      value: "TCP"
#   - name: Fixed
#     是否固定映射关系，默认不固定，pod删除后会生成新的外部IP及端口
#     value: true
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/gs-demo/gameserver:network
          name: gameserver
EOF
```

生成的GameServer中通过networkStatus字段查看游戏服网络信息：

```shell
  networkStatus:
    createTime: "2022-11-23T11:21:34Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: *************
      ports:
      - name: "80"
        port: "512"
        protocol: TCP
    internalAddresses:
    - ip: **********89
      ports:
      - name: "80"
        port: "80"
        protocol: TCP
    lastTransitionTime: "2022-11-23T11:21:34Z"
    networkType: AlibabaCloud-NATGW
```

访问 *************:512 即可

## 网络插件附录

当前支持的网络插件：
- Kubernetes-HostPort
- Kubernetes-NodePort
- Kubernetes-Ingress
- AlibabaCloud-NATGW
- AlibabaCloud-SLB
- AlibabaCloud-NLB
- AlibabaCloud-EIP
- AlibabaCloud-SLB-SharedPort
- AlibabaCloud-NLB-SharedPort
- Volcengine-CLB
- Volcengine-EIP
- HwCloud-ELB
- HwCloud-CCE-ELB
- HwCloud-CCE-EIP
---

### Kubernetes-HostPort

#### 插件名称

`Kubernetes-HostPort`

#### Cloud Provider

Kubernetes

#### 插件说明
- Kubernetes-HostPort利用宿主机网络，通过主机上的端口转发实现游戏服对外暴露服务。宿主机需要配置公网IP，有被公网访问的能力。

- 用户在配置文件中可自定义宿主机开放的端口段（默认为8000-9000），该网络插件可以帮助用户分配管理宿主机端口，尽量避免端口冲突。

- 该插件不支持网络隔离。

#### 网络参数

ContainerPorts

- 含义：填写提供服务的容器名以及对应暴露的端口和协议
- 填写格式：containerName:port1/protocol1,port2/protocol2,...（协议需大写） 比如：`game-server:25565/TCP`
- 是否支持变更：不支持，在创建时即永久生效，随pod生命周期结束而结束

#### 插件配置

```
[kubernetes]
enable = true
[kubernetes.hostPort]
#填写宿主机可使用的空闲端口段，用于为pod分配宿主机转发端口
max_port = 9000
min_port = 8000 
```

---

### Kubernetes-Ingress

#### 插件名称

`Kubernetes-Ingress`

#### Cloud Provider

Kubernetes

#### 插件说明

- 针对页游等需要七层网络模型的游戏场景，OKG提供了Ingress网络模型。该插件将会自动地为每个游戏服设置对应的访问路径，该路径与游戏服ID相关，每个游戏服各不相同。
- 是否支持网络隔离：否

#### 网络参数

Path

- 含义：访问路径。每个游戏服依据ID拥有各自的访问路径。
- 填写格式：将\<id>添加到原始路径(与HTTPIngressPath中Path一致)的任意位置，该插件将会生成游戏服ID对应的路径。例如，当设置路径为 /game\<id>，游戏服0对应路径为/game0，游戏服1对应路径为/game1，以此类推。
- 是否支持变更：支持

PathType

- 含义：路径类型。与HTTPIngressPath的PathType字段一致。
- 填写格式：与HTTPIngressPath的PathType字段一致。
- 是否支持变更：支持

Port

- 含义：游戏服暴露的端口值。
- 填写格式：端口数字
- 是否支持变更：支持

IngressClassName

- 含义：指定IngressClass的名称。与IngressSpec的IngressClassName字段一致。
- 填写格式：与IngressSpec的IngressClassName字段一致。
- 是否支持变更：支持

Host

- 含义：域名。与IngressRule的Host字段一致。
- 填写格式：与IngressRule的Host字段一致。
- 是否支持变更：支持

TlsHosts

- 含义：包含TLS证书的host列表。含义与IngressTLS的Hosts字段类似。
- 填写格式：host1,host2,... 例如，xxx.xx1.com,xxx.xx2.com
- 是否支持变更：支持

TlsSecretName

- 含义：与IngressTLS的SecretName字段一致。
- 填写格式：与IngressTLS的SecretName字段一致。
- 是否支持变更：支持

Annotation

- 含义：作为ingress对象的annotation
- 格式：key: value（注意:后有空格），例如：nginx.ingress.kubernetes.io/rewrite-target: /$2
- 是否支持变更：支持

Fixed

- 含义：是否需要保持ingress，让其不随pod的删除而删除
- 取值：true / false
- 是否支持变更：支持

_补充说明_

- 支持填写多个annotation，在networkConf中填写多个Annotation以及对应值即可，不区分填写顺序。
- 支持填写多个路径。路径、路径类型、端口按照填写顺序一一对应。当路径数目大于路径类型数目（或端口数目）时，无法找到对应关系的路径按照率先填写的路径类型（或端口）匹配。


#### 插件配置

无

#### 示例说明

GameServerSet中network字段声明如下：

```yaml
  network:
    networkConf:
    - name: IngressClassName
      value: nginx
    - name: Port
      value: "80"
    - name: Path
      value: /game<id>(/|$)(.*)
    - name: Path
      value: /test-<id>
    - name: Host
      value: test.xxx.cn-hangzhou.ali.com
    - name: PathType
      value: ImplementationSpecific
    - name: TlsHosts
      value: xxx.xx1.com,xxx.xx2.com
    - name: Annotation
      value: 'nginx.ingress.kubernetes.io/rewrite-target: /$2'
    - name: Annotation
      value: 'nginx.ingress.kubernetes.io/random: xxx'
    networkType: Kubernetes-Ingress
```

则会生成gss replicas对应数目的service与ingress对象。0号游戏服生成的ingress字段如下所示：
```yaml
spec:
  ingressClassName: nginx
  rules:
  - host: test.xxx.cn-hangzhou.ali.com
    http:
      paths:
      - backend:
          service:
            name: ing-nginx-0
            port:
              number: 80
        path: /game0(/|$)(.*)
        pathType: ImplementationSpecific
      - backend:
          service:
            name: ing-nginx-0
            port:
              number: 80
        path: /test-0
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - xxx.xx1.com
    - xxx.xx2.com
status:
  loadBalancer:
    ingress:
    - ip: 47.xx.xxx.xxx
```

其他序号的游戏服只有path字段与service name不同，生成的其他参数都相同。

对应的0号GameServer的networkStatus如下：
```yaml
  networkStatus:
    createTime: "2023-04-28T14:00:30Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: 47.xx.xxx.xxx
      ports:
      - name: /game0(/|$)(.*)
        port: 80
        protocol: TCP
      - name: /test-0
        port: 80
        protocol: TCP
    internalAddresses:
    - ip: 10.xxx.x.xxx
      ports:
      - name: /game0(/|$)(.*)
        port: 80
        protocol: TCP
      - name: /test-0
        port: 80
        protocol: TCP
    lastTransitionTime: "2023-04-28T14:00:30Z"
    networkType: Kubernetes-Ingress
```

---

### AlibabaCloud-NATGW

#### 插件名称

`AlibabaCloud-NATGW`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- AlibabaCloud-NATGW 使用阿里云公网网关作为游戏服对外服务的承载实体，外网流量通过DNAT规则转发至对应的游戏服中。

- 是否支持网络隔离：否

#### 网络参数

Ports

- 含义：填写pod需要暴露的端口
- 填写格式：port1,port2,port3… 例如：80,8080,8888
- 是否支持变更：不支持

Protocol

- 含义：填写服务的网络协议
- 填写格式：例如：tcp，默认为tcp
- 是否支持变更：不支持

Fixed

- 含义：是否固定访问IP/端口。若是，即使pod删除重建，网络内外映射关系不会改变
- 填写格式：false / true
- 是否支持变更：不支持

#### 插件配置

无

---

### AlibabaCloud-SLB

#### 插件名称

`AlibabaCloud-SLB`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- AlibabaCloud-SLB 使用阿里云经典四层负载均衡（SLB，又称CLB）作为对外服务的承载实体，在此模式下，不同游戏服将使用同一SLB的不同端口，此时SLB只做转发，并未均衡流量。

- 是否支持网络隔离：是

相关设计：https://github.com/openkruise/kruise-game/issues/20

#### 网络参数

SlbIds

- 含义：填写slb的id。可填写多个。
- 填写格式：各个slbId用,分割。例如：lb-9zeo7prq1m25ctpfrw1m7,lb-bp1qz7h50yd3w58h2f8je,...
- 是否支持变更：支持。可追加填写SLB实例id。建议不要更换正在被使用的实例id。

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议
- 格式：port1/protocol1,port2/protocol2,... (多协议相同端口 比如: 8000/TCPUDP)（协议需大写）
- 是否支持变更：支持

Fixed

- 含义：是否固定访问IP/端口。若是，即使pod删除重建，网络内外映射关系不会改变
- 填写格式：false / true
- 是否支持变更：支持

ExternalTrafficPolicyType

- 含义：Service LB 是否只转发给本地实例。若是Local， 创建Local类型Service, 配合cloud-manager只配置对应Node，可以保留客户端源IP地址
- 填写格式: Local/Cluster 默认Cluster
- 是否支持变更：不支持。跟是否固定IP/端口有关系，建议不更改

AllowNotReadyContainers

- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。

LBHealthCheckSwitch

- 含义：是否开启健康检查
- 格式：“on”代表开启，“off”代表关闭。默认为on
- 是否支持变更：支持

LBHealthCheckFlag

- 含义：是否开启http类型健康检查
- 格式：“on”代表开启，“off”代表关闭。默认为off
- 是否支持变更：支持

LBHealthCheckType

- 含义：健康检查协议
- 格式：填写 “tcp” 或者 “http”，默认为tcp
- 是否支持变更：支持

LBHealthCheckConnectTimeout

- 含义：健康检查响应的最大超时时间。
- 格式：单位：秒。取值范围[1, 300]。默认值为“5”
- 是否支持变更：支持

LBHealthyThreshold

- 含义：健康检查连续成功多少次后，将服务器的健康检查状态由失败判定为成功。
- 格式：取值范围[2, 10]。默认值为“2”
- 是否支持变更：支持

LBUnhealthyThreshold

- 含义：健康检查连续失败多少次后，将服务器的健康检查状态由成功判定为失败。
- 格式：取值范围[2, 10]。默认值为“2”
- 是否支持变更：支持

LBHealthCheckInterval

- 含义：健康检查的时间间隔。
- 格式：单位：秒。取值范围[1, 50]。默认值为“10”
- 是否支持变更：支持

LBHealthCheckProtocolPort

- 含义：http类型健康检查的协议及端口。
- 格式：多个值之间用英文半角逗号（,）分隔。如https:443,http:80
- 是否支持变更：支持

LBHealthCheckUri

- 含义：健康检查类型为HTTP时对应的检查路径。
- 格式：长度为1~80个字符，只能使用字母、数字、字符。 必须以正斜线（/）开头。
- 是否支持变更：支持

LBHealthCheckDomain

- 含义：健康检查类型为HTTP时对应的域名。
- 格式：特定域名长度限制1~80个字符，只能使用小写字母、数字、短划线（-）、半角句号（.）。
- 是否支持变更：支持

LBHealthCheckMethod

- 含义：健康检查类型为HTTP时对应的方法。
- 格式：“GET” 或者 “HEAD”
- 是否支持变更：支持

#### 插件配置
```
[alibabacloud]
enable = true
[alibabacloud.slb]
#填写slb可使用的空闲端口段，用于为pod分配外部接入端口，范围为200
max_port = 700
min_port = 500
```

---

### AlibabaCloud-SLB-SharedPort

#### 插件名称

`AlibabaCloud-SLB-SharedPort`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- AlibabaCloud-SLB-SharedPort 使用阿里云经典四层负载均衡（SLB，又称CLB）作为对外服务的承载实体。但与AlibabaCloud-SLB不同，`AlibabaCloud-SLB-SharedPort` 使用SLB同一端口转发流量，具有负载均衡的特点。
适用于游戏场景下代理（proxy）或网关等无状态网络服务。

- 是否支持网络隔离：是

#### 网络参数

SlbIds

- 含义：填写slb的id，支持填写多例
- 填写格式：例如：lb-9zeo7prq1m25ctpfrw1m7
- 是否支持变更：支持。

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：暂不支持。未来将支持

AllowNotReadyContainers

- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。

#### 插件配置

无

---

### AlibabaCloud-NLB
#### 插件名称

`AlibabaCloud-NLB`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- AlibabaCloud-NLB 使用阿里云网络型负载均衡作为对外服务的承载实体，在此模式下，不同游戏服将使用同一NLB的不同端口，此时NLB只做转发，并未均衡流量。

- 是否支持网络隔离：是

#### 网络参数

NlbIds

- 含义：填写nlb的id。可填写多个。
- 填写格式：各个nlbId用,分割。例如：nlb-ji8l844c0qzii1x6mc,nlb-26jbknebrjlejt5abu,...
- 是否支持变更：支持。可追加填写NLB实例id。建议不要更换正在被使用的实例id。

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：支持

Fixed

- 含义：是否固定访问IP/端口。若是，即使pod删除重建，网络内外映射关系不会改变
- 填写格式：false / true
- 是否支持变更：支持

AllowNotReadyContainers

- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。

LBHealthCheckFlag

- 含义：是否开启健康检查
- 格式：“on”代表开启，“off”代表关闭。默认为on
- 是否支持变更：支持

LBHealthCheckType

- 含义：健康检查协议
- 格式：填写 “tcp” 或者 “http”，默认为tcp
- 是否支持变更：支持

LBHealthCheckConnectPort

- 含义：健康检查的服务器端口。
- 格式：取值范围[0, 65535]。默认值为“0”
- 是否支持变更：支持

LBHealthCheckConnectTimeout

- 含义：健康检查响应的最大超时时间。
- 格式：单位：秒。取值范围[1, 300]。默认值为“5”
- 是否支持变更：支持

LBHealthyThreshold

- 含义：健康检查连续成功多少次后，将服务器的健康检查状态由失败判定为成功。
- 格式：取值范围[2, 10]。默认值为“2”
- 是否支持变更：支持

LBUnhealthyThreshold

- 含义：健康检查连续失败多少次后，将服务器的健康检查状态由成功判定为失败。
- 格式：取值范围[2, 10]。默认值为“2”
- 是否支持变更：支持

LBHealthCheckInterval

- 含义：健康检查的时间间隔。
- 格式：单位：秒。取值范围[1, 50]。默认值为“10”
- 是否支持变更：支持

LBHealthCheckUri

- 含义：健康检查类型为HTTP时对应的检查路径。
- 格式：长度为1~80个字符，只能使用字母、数字、字符。 必须以正斜线（/）开头。
- 是否支持变更：支持

LBHealthCheckDomain

- 含义：健康检查类型为HTTP时对应的域名。
- 格式：特定域名长度限制1~80个字符，只能使用小写字母、数字、短划线（-）、半角句号（.）。
- 是否支持变更：支持

LBHealthCheckMethod

- 含义：健康检查类型为HTTP时对应的方法。
- 格式：“GET” 或者 “HEAD”
- 是否支持变更：支持

#### 插件配置
```
[alibabacloud]
enable = true
[alibabacloud.nlb]
#填写nlb可使用的空闲端口段，用于为pod分配外部接入端口，范围为500
max_port = 1500
min_port = 1000
```

#### 示例说明

```
cat <<EOF | kubectl apply -f -
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gs-nlb
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkConf:
    - name: NlbIds
      value: nlb-muyo7fv6z646ygcxxx
    - name: PortProtocols
      value: "80"
    - name: Fixed
      value: "true"
    - name: LBHealthCheckFlag
      value: "off"
    networkType: AlibabaCloud-NLB
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/gs-demo/gameserver:network
          name: gameserver
EOF
```

生成的GameServer中通过networkStatus字段查看游戏服网络信息：

```
  networkStatus:
    createTime: "2024-04-28T12:41:56Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - endPoint: nlb-muyo7fv6z646ygcxxx.cn-xxx.nlb.aliyuncs.com
      ip: ""
      ports:
      - name: "80"
        port: 1047
        protocol: TCP
    internalAddresses:
    - ip: **********
      ports:
      - name: "80"
        port: 80
        protocol: TCP
    lastTransitionTime: "2024-04-28T12:41:56Z"
    networkType: AlibabaCloud-NLB
```

访问 nlb-muyo7fv6z646ygcxxx.cn-xxx.nlb.aliyuncs.com:1047 即可

---

### AlibabaCloud-EIP

#### 插件名称

`AlibabaCloud-EIP`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- 为每个GameServer单独分配EIP
- 暴露的公网访问端口与容器中监听的端口一致，通过安全组管理
- 需要在ACK集群安装最新版本ack-extend-network-controller组件，详情请见[组件说明页](https://cs.console.aliyun.com/#/next/app-catalog/ack/incubator/ack-extend-network-controller)

#### 网络参数

ReleaseStrategy

- 含义：EIP回收策略。
- 填写格式：
    - Follow：默认值，跟随游戏服生命周期。当游戏服被删除时，EIP也将被回收。
    - Never：不删除podEIP。当不需要时需要手动删除这个podEIP。(通过kubectl delete podeip {游戏服name} -n {游戏服所在namespace})
    - 可直接配置过期时间，例如：5m30s，表示Pod删除5.5分钟后删除podEIP。支持Go类型时间表达式。
- 是否支持变更：否

PoolId

- 含义：EIP地址池ID。可为空，则不使用EIP地址池。
- 是否支持变更：否

ResourceGroupId

- 含义：EIP资源组ID。可为空，则使用默认资源组。
- 是否支持变更：否

Bandwidth

- 含义：峰值带宽。单位：Mbps。可为空，默认为5
- 是否支持变更：否

BandwidthPackageId

- 含义：要绑定已有的共享带宽包ID。可为空，则EIP不绑定共享带宽包。
- 是否支持变更：否

ChargeType

- 含义：EIP的计费方式。
- 填写格式：
    - PayByTraffic：按使用流量计费。
    - PayByBandwidth：按带宽计费，为默认值。
- 是否支持变更：否

Description

- 含义：对EIP资源的描述。
- 是否支持变更：否

#### 插件配置

无

#### 示例说明

```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: eip-nginx
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: AlibabaCloud-EIP
    networkConf:
      - name: ReleaseStrategy
        value: Never
      - name: Bandwidth
        value: "3"
      - name: ChargeType
        value: PayByTraffic
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

生成的gameserver eip-nginx-0 networkStatus字段如下所示：

```yaml
  networkStatus:
    createTime: "2023-07-17T10:10:18Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: 47.98.xxx.xxx
    internalAddresses:
    - ip: ************
    lastTransitionTime: "2023-07-17T10:10:18Z"
    networkType: AlibabaCloud-EIP
```

生成对应的podeip eip-nginx-0 如下所示：

```yaml
apiVersion: alibabacloud.com/v1beta1
kind: PodEIP
metadata:
  annotations:
    k8s.aliyun.com/eip-controller: ack-extend-network-controller
  creationTimestamp: "2023-07-17T09:58:12Z"
  finalizers:
  - podeip-controller.alibabacloud.com/finalizer
  generation: 1
  name: eip-nginx-1
  namespace: default
  resourceVersion: "41443319"
  uid: 105a9575-998e-4e17-ab91-8f2597eeb55f
spec:
  allocationID: eip-xxx
  allocationType:
    releaseStrategy: Never
    type: Auto
status:
  eipAddress: 47.98.xxx.xxx
  internetChargeType: PayByTraffic
  isp: BGP
  networkInterfaceID: eni-xxx
  podLastSeen: "2023-07-17T10:36:02Z"
  privateIPAddress: ************
  resourceGroupID: rg-xxx
  status: InUse
```

此外，生成的EIP资源在阿里云控制台中会以{pod namespace}/{pod name}命名，与每一个游戏服一一对应。

---

### Volcengine-EIP

#### 插件名称

`Volcengine-EIP`

#### Cloud Provider

Volcengine

#### 插件说明

- 为每个 GameServer 单独分配或绑定火山引擎的弹性公网 IP（EIP），支持通过注解或 networkConf 指定已有 EIP 或自动分配新 EIP。
- 暴露的公网访问端口与容器中监听的端口一致，安全组策略需自行配置。
- 适用于需要公网访问能力的游戏服务器等场景。
- 需要在集群中安装 `vpc-cni-controlplane` 组件，详情请见[组件说明页](https://www.volcengine.com/docs/6460/101015)
`

#### 网络参数
> 更多参数请参考：https://www.volcengine.com/docs/6460/1152127

name

- EIP 名称，未指定时由系统自动生成。
- 是否支持变更：否

isp

- EIP 线路类型
- 是否支持变更：否

projectName

- 含义：EIP 所属项目名称，默认为 default。
- 是否支持变更：否

bandwidth

- 含义：峰值带宽。单位：Mbps。可为空
- 是否支持变更：否

bandwidthPackageId

- 含义：要绑定的共享带宽包 ID。可为空，则 EIP 不绑定共享带宽包。
- 是否支持变更：否

billingType

- 含义：EIP 的计费方式。
- EIP 计费方式，取值：
  - 2：（默认值）按量计费-按带宽上限计费。
  - 3：按量计费-按实际流量计费。
- 是否支持变更：否

description

- 含义：对 EIP 资源的描述。
- 是否支持变更：否

#### 注解参数

- `vke.volcengine.com/primary-eip-id`：指定已有 EIP ID，Pod 启动时绑定该 EIP。

#### 插件配置

无

#### 示例说明

```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: eip-nginx
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: Volcengine-EIP
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

生成的 GameServer networkStatus 字段如下所示：

```yaml
  networkStatus:
    createTime: "2025-01-17T10:10:18Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: 106.xx.xx.xx
    internalAddresses:
    - ip: ************
    lastTransitionTime: "2025-01-17T10:10:18Z"
    networkType: Volcengine-EIP
```

Pod 相关注解示例：

```yaml
metadata:
  annotations:
    vke.volcengine.com/primary-eip-id: eip-xxx
    vke.volcengine.com/primary-eip-attributes: '{"bandwidth":3,"billingType":"2"}'
```

EIP 资源会在火山引擎控制台以 `{pod namespace}/{pod name}` 命名，与每一个游戏服一一对应。

---

### AlibabaCloud-NLB-SharedPort

#### 插件名称

`AlibabaCloud-NLB-SharedPort`

#### Cloud Provider

AlibabaCloud

#### 插件说明

- AlibabaCloud-NLB-SharedPort 使用阿里云网络型负载均衡（NLB）作为对外服务的承载实体。其与AlibabaCloud-SLB-SharedPort作用类似。
  适用于游戏场景下代理（proxy）或网关等无状态网络服务。

- 是否支持网络隔离：是

#### 网络参数

SlbIds

- 含义：填写nlb的id，暂不支持填写多例
- 填写格式：例如：nlb-9zeo7prq1m25ctpfrw1m7
- 是否支持变更：暂不支持。

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：暂不支持。

AllowNotReadyContainers

- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。

#### 插件配置

无

#### 示例说明

部署一个具有两个容器的GameServerSet，一个容器名为app-2048，另一个为sidecar。
指定网络参数 AllowNotReadyContainers 为 sidecar，则在sidecar原地更新时整个pod依然会提供服务，不会断流。

```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gss-2048-nlb
  namespace: default
spec:
  replicas: 3
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 100%
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: AlibabaCloud-NLB-SharedPort
    networkConf:
      - name: NlbIds
        value: nlb-26jbknebrjlejt5abu
      - name: PortProtocols
        value: 80/TCP
      - name: AllowNotReadyContainers
        value: sidecar
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-beijing.aliyuncs.com/acs/2048:v1.0
          name: app-2048
          volumeMounts:
            - name: shared-dir
              mountPath: /var/www/html/js
        - image: registry.cn-beijing.aliyuncs.com/acs/2048-sidecar:v1.0
          name: sidecar
          args:
            - bash
            - -c
            - rsync -aP /app/js/* /app/scripts/ && while true; do echo 11;sleep 2; done
          volumeMounts:
            - name: shared-dir
              mountPath: /app/scripts
      volumes:
        - name: shared-dir
          emptyDir: {}
```

部署成功后，将sidecar镜像更新到v2.0版本，同时观察对应endpoint情况:
```bash
kubectl get ep -w | grep nlb-26jbknebrjlejt5abu
nlb-26jbknebrjlejt5abu      ***********:80,************:80,**************:80    10m

```

等待整个更新过程结束，可以发现ep没有任何变化，说明并未进行摘流。

---

### TencentCloud-CLB

#### 插件名称

`TencentCloud-CLB`

#### Cloud Provider

TencentCloud

#### 插件说明

- TencentCloud-CLB 使用腾讯云负载均衡器（CLB）作为对外服务的承载实体，在此模式下，不同游戏服使用 CLB 的不同端口对外暴露，此时 CLB 只做转发，并未均衡流量。
- 需安装 [tke-extend-network-controller](https://github.com/tkestack/tke-extend-network-controller) 网络插件(可通过 TKE 应用市场安装)。
- 是否支持网络隔离：是。

#### 网络参数

ClbIds

- 含义：填写clb的id。可填写多个。
- 填写格式：各个clbId用,分割。例如：lb-xxxx,lb-yyyy,...
- 是否支持变更：支持。

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议。
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：支持。

#### 插件配置

```
[tencentcloud]
enable = true
[tencentcloud.clb]
# 填写clb可使用的空闲端口段，用于为pod分配外部接入端口
min_port = 1000
max_port = 1100
```

---

### HwCloud-ELB

#### Plugin name

`HwCloud-ELB`

#### Cloud Provider

HwCloud

#### Plugin description

- HwCloud-ELB 使用华为云负载均衡器（ELB）作为对外服务的承载实体，在此模式下，不同游戏服使用 ELB 的不同端口对外暴露，此时 ELB 只做转发，并未均衡流量。
- 需安装https://github.com/kubernetes-sigs/cloud-provider-huaweicloud。
- 是否支持网络隔离：是。

#### Network parameters

ElbIds

- 含义: elb 的ID, 可填写多个 (必须至少1)
- 填写格式: 例如 "lb-9zeo7prq1m25ctpfrw1m7,lb-bp1qz7h50yd3w58h2f8je"
- 是否支持变更：支持，只追加

PortProtocols

- 含义：pod暴露的端口及协议，支持填写多个端口/协议。
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：支持。

Fixed

- 含义：是否固定访问IP/端口。若是，即使pod删除重建，网络内外映射关系不会改变
- 填写格式：false / true
- 是否支持变更：支持

AllowNotReadyContainers

- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。


ExternalTrafficPolicyType

- 含义：Service LB 是否只转发给本地实例。若是Local， 创建Local类型Service, 配合cloud-manager只配置对应Node，可以保留客户端源IP地址
- 填写格式: Local/Cluster 默认Cluster
- 是否支持变更：不支持。跟是否固定IP/端口有关系，建议不更改


LB config parameters consistent with huawei cloud ccm https://github.com/kubernetes-sigs/cloud-provider-huaweicloud/blob/master/docs/usage-guide.md

LBHealthCheckFlag

- 含义：是否开启健康检查
- 填写格式: on 或者 off ，默认 on
- 是否支持变更：支持


LBHealthCheckOption

- 含义：健康检查的配置信息
- 填写格式: json 字符串 比如 {"delay": 3, "timeout": 15, "max_retries": 3} 默认空
- 是否支持变更：支持


ElbClass

- 含义：elb 类型
- 填写格式: 独享（dedicated） 或者 共享（shared） 默认dedicated
- 是否支持变更：不支持


ElbConnLimit

- 含义：共享型LB的连接限制数
- 填写格式: -1 到 ********** 默认-1 不限制
- 是否支持变更：不支持

ElbLbAlgorithm

- 含义：RS 的 LB 算法
- 填写格式:  ROUND_ROBIN,LEAST_CONNECTIONS,SOURCE_IP default ROUND_ROBIN
- 是否支持变更： 支持

ElbSessionAffinityFlag

- 含义：是否开启session亲和
- 填写格式: on 或者 off 默认 off
- 是否支持变更：不支持

ElbSessionAffinityOption

- 含义：session亲和的超时配置
- 填写格式: json 字符串 比如 {"type": "SOURCE_IP", "persistence_timeout": 15}
- 是否支持变更：支持

ElbTransparentClientIP

- 含义：是否透传源IP
- 填写格式: true 或者 false 默认 false
- 是否支持变更：支持

ElbXForwardedHost

- 含义：是否重写X-Forwarded-Host头
- 填写格式: true 或者 false 默认 false
- 是否支持变更：支持

ElbIdleTimeout

- 含义：rs 的空闲超时时间，最后会彻底删除
- 填写格式: 0 到 4000，默认不设置使用LB的默认配置
- 是否支持变更：支持

ElbRequestTimeout

- 含义：http，https请求超时时间
- 填写格式: 1 到 300，默认不设置使用LB的默认配置
- 是否支持变更：支持

ElbResponseTimeout

- 含义：http，https响应超时时间
- 填写格式: 1 到 300，默认不设置使用LB的默认配置
- 是否支持变更：支持


#### Plugin configuration
```
[hwcloud]
enable = true
[hwcloud.elb]
max_port = 700
min_port = 500
block_ports = []
```

---

#### 示例说明

```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: clb-nginx
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: TencentCloud-CLB
    networkConf:
      - name: ClbIds
        value: "lb-3ip9k5kr,lb-4ia8k0yh"
      - name: PortProtocols
        value: "80/TCP,7777/UDP"
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

生成的 gameserver clb-nginx-0 networkStatus 字段如下所示：

```yaml
  networkStatus:
    createTime: "2024-10-28T03:16:20Z"
    currentNetworkState: Ready
    desiredNetworkState: Ready
    externalAddresses:
    - ip: *************
      ports:
      - name: "80"
        port: 1002
        protocol: TCP
    - ip: *************
      ports:
      - name: "7777"
        port: 1003
        protocol: UDP
    internalAddresses:
    - ip: ************
      ports:
      - name: "80"
        port: 80
        protocol: TCP
    - ip: ************
      ports:
      - name: "7777"
        port: 7777
        protocol: UDP
    lastTransitionTime: "2024-10-28T03:16:20Z"
    networkType: TencentCloud-CLB
```
---
### HwCloud-CCE-ELB

#### 插件名称
`HwCloud-CCE-ELB`   
**注意**: 
- 此插件仅仅适用于华为云的CCE Standard和CCE Turbo集群.
- 如果使用已有的ELB, 请保证ELB的VPC和CCE集群的VPC一致, 否则无法访问.

#### Cloud Provider
HuaweiCloud

#### 插件说明
- HwCloud-ELB 使用华为云负载均衡器（ELB）作为对外服务的承载实体，可以通过弹性负载均衡（ELB）将外部流量向集群内的多个Pod进行分发，与NodePort类型相比提供了高可靠的保障。
- 支持的annotations,可以参看文档: https://support.huaweicloud.com/usermanual-cce/cce_10_0681.html
- 暴露的公网访问端口与容器中监听的端口一致.
- 可以绑定安全组进行管理([使用注解为Pod绑定安全组](https://support.huaweicloud.com/usermanual-cce/cce_10_0897.html)),需要CCE Turbo集群才支持。
  - Pod的网卡使用annotation配置的安全组: `yangtse.io/security-group-ids`
  - Pod的网卡在使用已有安全组的基础上，额外再增加annotation配置的安全组: `yangtse.io/additional-security-group-ids`
- 是否支持网络隔离：是。

#### 网络参数
PortProtocols
- 含义：pod暴露的端口及协议，支持填写多个端口/协议。
- 格式：port1/protocol1,port2/protocol2,...（协议需大写）
- 是否支持变更：支持。

Fixed
- 含义：是否固定访问IP/端口。若是，即使pod删除重建，网络内外映射关系不会改变
- 填写格式：false / true
- 是否支持变更：支持

AllowNotReadyContainers
- 含义：在容器原地升级时允许不断流的对应容器名称，可填写多个
- 格式：{containerName_0},{containerName_1},... 例如：sidecar
- 是否支持变更：在原地升级过程中不可变更。

ExternalTrafficPolicyType
- 含义：Service LB 是否只转发给本地实例。若是Local， 创建Local类型Service, 配合cloud-manager只配置对应Node，可以保留客户端源IP地址
- 填写格式: Local/Cluster 默认Cluster
- 是否支持变更：不支持。跟是否固定IP/端口有关系，建议不更改

其他与华为CCE集群相关的参数  
参考文档里的annotation的key与value进行填写 
- [负载均衡（LoadBalancer）](https://support.huaweicloud.com/usermanual-cce/cce_10_0014.html)


#### Plugin configuration
这里的端口范围可以根据您的业务范围自行配置, block_ports请参考这个issue: https://github.com/openkruise/kruise-game/issues/174
```
[hwcloud]
enable = true
[hwcloud.cce.elb]
max_port = 65535
min_port = 32768
block_ports = []
```

---
#### 示例说明
使用已有的elb(https://support.huaweicloud.com/usermanual-cce/cce_10_0385.html#section1),其他可用的annotation请参考华为云文档  
```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: hw-cce-elb-nginx
  namespace: default
spec:
  replicas: 2
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: HwCloud-CCE-ELB
    networkConf:
      - name: PortProtocols
        value: "80/TCP"
      - name: kubernetes.io/elb.class # ELB实例的类型
        value: performance
      - name: kubernetes.io/elb.id # ELB实例的ID
        value: 8f4cf216-a659-40dc-8c77-xxxx
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

生成的svc如下所示,可以看到2个svc都指向了同一个elb:
```yaml
apiVersion: v1
kind: Service
metadata:
  annotations:
    game.kruise.io/network-config-hash: "3594992400"
    kubernetes.io/elb.class: performance
    kubernetes.io/elb.connection-drain-enable: "true"
    kubernetes.io/elb.connection-drain-timeout: "300"
    kubernetes.io/elb.id: 8f4cf216-a659-40dc-8c77-xxxx
    kubernetes.io/elb.mark: "0"
  creationTimestamp: "2025-07-23T08:15:09Z"
  finalizers:
    - service.kubernetes.io/load-balancer-cleanup
  name: hw-cce-elb-nginx-0
  namespace: kruise-game-system
  ownerReferences:
    - apiVersion: v1
      blockOwnerDeletion: true
      controller: true
      kind: Pod
      name: hw-cce-elb-nginx-0
      uid: 4f9f37f9-16d4-4ee7-b553-9b6e0039c5d5
  resourceVersion: "13369506"
  uid: 23815818-a626-4be3-b31f-4b95a4f89786
spec:
  allocateLoadBalancerNodePorts: true
  clusterIP: 10.247.213.xxx
  clusterIPs:
    - 10.247.213.xxx
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  loadBalancerIP: 192.168.0.xxx
  ports:
    - name: 80-tcp
      nodePort: 30622
      port: 3308
      protocol: TCP
      targetPort: 80
    - name: 80-udp
      nodePort: 30622
      port: 3308
      protocol: UDP
      targetPort: 80
  selector:
    statefulset.kubernetes.io/pod-name: hw-cce-elb-nginx-0
  sessionAffinity: None
  type: LoadBalancer
status:
  loadBalancer:
    ingress:
      - ip: 192.168.0.xxx
      - ip: 189.1.225.xxx

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    game.kruise.io/network-config-hash: "3594992400"
    kubernetes.io/elb.class: performance
    kubernetes.io/elb.connection-drain-enable: "true"
    kubernetes.io/elb.connection-drain-timeout: "300"
    kubernetes.io/elb.id: 8f4cf216-a659-40dc-8c77-xxxx
    kubernetes.io/elb.mark: "0"
  creationTimestamp: "2025-07-23T08:15:08Z"
  finalizers:
    - service.kubernetes.io/load-balancer-cleanup
  name: hw-cce-elb-nginx-1
  namespace: kruise-game-system
  ownerReferences:
    - apiVersion: v1
      blockOwnerDeletion: true
      controller: true
      kind: Pod
      name: hw-cce-elb-nginx-1
      uid: 0f42b430-49ba-4203-8b50-4be059619b79
  resourceVersion: "13369489"
  uid: 92a56054-ad92-4dbd-9d1b-e717e0a14af2
spec:
  allocateLoadBalancerNodePorts: true
  clusterIP: 10.247.14.xxx
  clusterIPs:
    - 10.247.14.xxx
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  loadBalancerIP: 192.168.0.xxx
  ports:
    - name: 80-tcp
      nodePort: 32227
      port: 3611
      protocol: TCP
      targetPort: 80
    - name: 80-udp
      nodePort: 32227
      port: 3611
      protocol: UDP
      targetPort: 80
  selector:
    statefulset.kubernetes.io/pod-name: hw-cce-elb-nginx-1
  sessionAffinity: None
  type: LoadBalancer
status:
  loadBalancer:
    ingress:
      - ip: 192.168.0.xxx
      - ip: 189.1.225.xxx
```
生成的svc如下,可以看到2个svc指向了同一个ip, 只是端口不同:
```bash
kubectl get svc |grep hw-cce-elb-nginx
hw-cce-elb-nginx-0                           LoadBalancer   10.247.213.xxx   189.1.225.xxx,192.168.0.xxx   3308:30622/TCP,3308:30622/UDP   2m3s
hw-cce-elb-nginx-1                           LoadBalancer   10.247.14.xxx    189.1.225.xxx,192.168.0.xxx   3611:32227/TCP,3611:32227/UDP   2m4s
```
---
自动创建ELB并绑定到创建出来的svc上面 
**注意**: 
- 自动创建elb,如果为多副本,每个svc都使用自动创建的elb,每个elb的id是不一样的,暴露的external ip也是不一样的.
- 当删除svc时, 关联的自动创建的elb也会被删除.
```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: hw-cce-elb-auto-performance
  namespace: kruise-game-system
spec:
  replicas: 2
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: HwCloud-CCE-ELB
    networkConf:
      - name: PortProtocols
        value: "80/TCP"
      - name: kubernetes.io/elb.class
        value: performance # ELB实例的类型
      - name: kubernetes.io/elb.autocreate # 自动创建ELB的选项: https://support.huaweicloud.com/usermanual-cce/cce_10_0385.html#section21
        value: '{
                  "type": "public",
                  "bandwidth_name": "bandwidth-xxxx",
                  "bandwidth_chargemode": "traffic",
                  "bandwidth_size": 5,
                  "bandwidth_sharetype": "PER",
                  "eip_type": "5_bgp",
                  "available_zone": [
                     "ap-southeast-1a",
                     "ap-southeast-1b"
                  ],
                  "l4_flavor_name": "L4_flavor.elb.s1.small"
                }'
      - name: kubernetes.io/elb.enterpriseID # 创建出来的负载均衡所属企业项目ID
        value: 'aff97261-4dbd-4593-8236-xxxx'
      - name: kubernetes.io/elb.lb-algorithm
        value: ROUND_ROBIN # 负载均衡器算法
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
         
```
生成的svc如下所示,可以看到2个svc都指向不同的elb:
```yaml
apiVersion: v1
kind: Service
metadata:
  annotations:
    game.kruise.io/network-config-hash: "3090934611"
    kubernetes.io/elb.autocreate: '{ "type": "public", "bandwidth_name": "bandwidth-89f0",
      "bandwidth_chargemode": "traffic", "bandwidth_size": 5, "bandwidth_sharetype":
      "PER", "eip_type": "5_bgp", "available_zone": [ "ap-southeast-1a", "ap-southeast-1b"
      ], "l4_flavor_name": "L4_flavor.elb.s1.small" }'
    kubernetes.io/elb.class: performance
    kubernetes.io/elb.eip-id: 566d5f4c-3484-4d7e-aa6b-xxxx
    kubernetes.io/elb.enterpriseID: aff97261-4dbd-4593-8236-xxxx
    kubernetes.io/elb.id: 75e06e8b-a246-48cb-b05c-xxxx
    kubernetes.io/elb.lb-algorithm: ROUND_ROBIN
    kubernetes.io/elb.mark: "0"
  creationTimestamp: "2025-07-23T09:25:01Z"
  finalizers:
    - service.kubernetes.io/load-balancer-cleanup
  name: hw-cce-elb-auto-performance-0
  namespace: kruise-game-system
  ownerReferences:
    - apiVersion: v1
      blockOwnerDeletion: true
      controller: true
      kind: Pod
      name: hw-cce-elb-auto-performance-0
      uid: 1da0edf4-f45d-4635-8db0-ed5ccea2441d
  resourceVersion: "13401553"
  uid: 13efd440-65a7-4b45-bafc-2268102a4fd7
spec:
  allocateLoadBalancerNodePorts: true
  clusterIP: 10.247.50.xxx
  clusterIPs:
    - 10.247.50.xxx
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  loadBalancerIP: 49.0.251.xxx
  ports:
    - name: 80-tcp
      nodePort: 30918
      port: 1
      protocol: TCP
      targetPort: 80
  selector:
    statefulset.kubernetes.io/pod-name: hw-cce-elb-auto-performance-0
  sessionAffinity: None
  type: LoadBalancer
status:
  loadBalancer:
    ingress:
      - ip: 49.0.251.xxx
      - ip: 192.168.1.xxx
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    game.kruise.io/network-config-hash: "3090934611"
    kubernetes.io/elb.autocreate: '{ "type": "public", "bandwidth_name": "bandwidth-89f0",
      "bandwidth_chargemode": "traffic", "bandwidth_size": 5, "bandwidth_sharetype":
      "PER", "eip_type": "5_bgp", "available_zone": [ "ap-southeast-1a", "ap-southeast-1b"
      ], "l4_flavor_name": "L4_flavor.elb.s1.small" }'
    kubernetes.io/elb.class: performance
    kubernetes.io/elb.eip-id: 4a5396b1-e750-4ba5-a5d3-xxxx
    kubernetes.io/elb.enterpriseID: aff97261-4dbd-4593-8236-xxxx
    kubernetes.io/elb.id: b093db79-3c3e-4e77-a2ee-xxxx
    kubernetes.io/elb.lb-algorithm: ROUND_ROBIN
    kubernetes.io/elb.mark: "0"
  creationTimestamp: "2025-07-23T09:25:01Z"
  finalizers:
    - service.kubernetes.io/load-balancer-cleanup
  name: hw-cce-elb-auto-performance-1
  namespace: kruise-game-system
  ownerReferences:
    - apiVersion: v1
      blockOwnerDeletion: true
      controller: true
      kind: Pod
      name: hw-cce-elb-auto-performance-1
      uid: abfc9ad1-1ae3-45fa-b956-4617c465a44f
  resourceVersion: "13401664"
  uid: 01dd8e13-b1c8-4d9f-8b1c-13c2f001c614
spec:
  allocateLoadBalancerNodePorts: true
  clusterIP: 10.247.196.xxx
  clusterIPs:
    - 10.247.196.xxx
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  loadBalancerIP: 150.40.245.xxx
  ports:
    - name: 80-tcp
      nodePort: 30942
      port: 1
      protocol: TCP
      targetPort: 80
  selector:
    statefulset.kubernetes.io/pod-name: hw-cce-elb-auto-performance-1
  sessionAffinity: None
  type: LoadBalancer
status:
  loadBalancer:
    ingress:
      - ip: 150.40.245.xxx
      - ip: 192.168.1.xxx
```
生成的svc如下,可以看到2个svc指向不同的external ip:
```bash
kubectl get svc |grep hw-cce-elb-auto-performance
hw-cce-elb-auto-performance-0                    LoadBalancer   10.247.50.xxx    192.168.1.xxx,49.0.251.xxx      1:30918/TCP                       4m29s
hw-cce-elb-auto-performance-1                    LoadBalancer   10.247.196.xxx   150.40.245.xxx,192.168.1.xxx    1:30942/TCP                       4m29s
```

### HwCloud-CCE-EIP
#### 插件名称
`HwCloud-CCE-EIP`  
**注意**: 此插件仅仅适用于华为云的CCE Turbo集群  

#### Cloud Provider
HuaweiCloud

#### 插件说明
- 仅适用于华为云CCE Turbo集群: https://support.huaweicloud.com/usermanual-cce/cce_10_0284.html#section1
- 为每个pod单独分配EIP
- 暴露的公网访问端口与容器中监听的端口一致，可以绑定安全组进行管理([使用注解为Pod绑定安全组](https://support.huaweicloud.com/usermanual-cce/cce_10_0897.html))
  - Pod的网卡使用annotation配置的安全组: `yangtse.io/security-group-ids`
  - Pod的网卡在使用已有安全组的基础上，额外再增加annotation配置的安全组: `yangtse.io/additional-security-group-ids`
- 支持自动创建EIP并绑定到对应的pod上, 或者使用已有的EIP
- 自动创建EIP时不支持指定`企业项目`
#### 网络参数
参考华为云的文档: https://support.huaweicloud.com/usermanual-cce/cce_10_0734.html, 本插件支持这个页面所有的annotation.

#### 插件配置
无

#### 示例说明
独占带宽EIP跟随Pod创建, 其他可用的annotation请参考华为云文档  
注意: 这里创建出来的EIP属于`default`这个企业项目, 华为云CCE Turbo集群暂时不支持在这种模式下指定`企业项目`  
```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: hw-cce-eip-performance
  namespace: default
spec:
  replicas: 2
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: HwCloud-CCE-EIP
    networkConf:
      # https://support.huaweicloud.com/usermanual-cce/cce_10_0734.html
      - name: yangtse.io/pod-with-eip
        value: "true"
      - name: yangtse.io/eip-bandwidth-size
        value: "5"
      - name: yangtse.io/eip-network-type
        value: "5_bgp"
      - name: yangtse.io/eip-charge-mode
        value: "traffic"
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

生成的pod的annotation如下,`yangtse.io/allocated-eip-id`对应的eip可以在华为云的弹性公网IP详情中查看, `yangtse.io/allocated-ipv4-eip`
即为pod的eip
```yaml
apiVersion: v1
kind: Pod
metadata:
  annotations:
    apps.kruise.io/runtime-containers-meta: '{"containers":[{"name":"nginx","containerID":"containerd://302f710dc7fb5771be5b16a31de84ff457fd84c9aa1ce00b7e7f2ddc3b7c3978","restartCount":0,"hashes":{"plainHash":2641665875,"plainHashWithoutResources":0,"extractedEnvFromMetadataHash":86995377}}]}'
    game.kruise.io/network-conf: '[{"name":"yangtse.io/pod-with-eip","value":"true"},{"name":"yangtse.io/eip-bandwidth-size","value":"5"},{"name":"yangtse.io/eip-network-type","value":"5_bgp"},{"name":"yangtse.io/eip-charge-mode","value":"traffic"}]'
    game.kruise.io/network-status: '{"currentNetworkState":"Ready","createTime":null,"lastTransitionTime":null}'
    game.kruise.io/network-trigger-time: "2025-07-16 17:03:07"
    game.kruise.io/network-type: HwCloud-EIP
    game.kruise.io/opsState-last-changed-time: "2025-07-16 17:03:07"
    game.kruise.io/state-last-changed-time: "2025-07-16 09:03:13"
    lifecycle.apps.kruise.io/timestamp: "2025-07-16T09:03:03Z"
    yangtse.io/allocated-eip-id: 3a52ca79-d78d-4fc2-8590-xxxx
    yangtse.io/allocated-ipv4-eip: 94.74.110.xxx
    yangtse.io/eip-bandwidth-size: "5"
    yangtse.io/eip-charge-mode: traffic
    yangtse.io/eip-network-type: 5_bgp
    yangtse.io/pod-with-eip: "true"
```

使用已有的EIP, spec.network.networkConf添加`yangtse.io/eip-id`,你需要事先在华为云创建好EIP
```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: hw-cce-eip-exist
  namespace: kruise-game-system
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: HwCloud-CCE-EIP
    networkConf:
      - name: yangtse.io/eip-id
        value: "7ec474aa-3bd9-46a2-a45c-xxxx" # 使用已有的EIP
  gameServerTemplate:
    spec:
      containers:
        - image: nginx
          name: nginx
```

pod的yaml, 可以看到pod的annotations里`yangtse.io/allocated-eip-id`为我们指定的EIP,登录华为云EIP控制台,可以看到这个EIP已经绑定了pod
```yaml
apiVersion: v1
kind: Pod
metadata:
  annotations:
    apps.kruise.io/runtime-containers-meta: '{"containers":[{"name":"nginx","containerID":"containerd://0fc9de69e30b48cf13ad2d2c6f5fe3be86e48e922a982dbb77b53ffd0ca6f54b","restartCount":0,"hashes":{"plainHash":2957831032,"plainHashWithoutResources":0,"extractedEnvFromMetadataHash":86995377}}]}'
    game.kruise.io/network-conf: '[{"name":"yangtse.io/eip-id","value":"7ec474aa-3bd9-46a2-a45c-xxxx"}]'
    game.kruise.io/network-status: '{"currentNetworkState":"Ready","createTime":null,"lastTransitionTime":null}'
    game.kruise.io/network-trigger-time: "2025-07-18 15:38:21"
    game.kruise.io/network-type: HwCloud-EIP
    game.kruise.io/opsState-last-changed-time: "2025-07-18 15:38:21"
    game.kruise.io/state-last-changed-time: "2025-07-18 15:38:31"
    lifecycle.apps.kruise.io/timestamp: "2025-07-18T07:38:13Z"
    yangtse.io/allocated-eip-id: 7ec474aa-3bd9-46a2-a45c-xxxx
    yangtse.io/allocated-ipv4-eip: 159.138.21.xxx
    yangtse.io/eip-id: 7ec474aa-3bd9-46a2-a45c-xxxx
  creationTimestamp: "2025-07-18T07:38:14Z
# other info ignored
```
## 获取网络信息

GameServer Network Status可以通过两种方式获取

### k8s API
调用K8s API，获取GameServer对象。该方式适用于中心组件，通常在匹配服务获取游戏服网络信息，用于路由选择等。

### DownwardAPI
通过DownwardAPI，将网络信息下沉至业务容器中，供游戏服业务容器使用

该方法的示例如下:

```yaml
apiVersion: game.kruise.io/v1alpha1
kind: GameServerSet
metadata:
  name: gs-slb
  namespace: default
spec:
  replicas: 1
  updateStrategy:
    rollingUpdate:
      podUpdatePolicy: InPlaceIfPossible
  network:
    networkType: AlibabaCloud-SLB
    networkConf:
    - name: SlbIds
      value: "xxx"
    - name: PortProtocols
      value: "xxx"
    - name: Fixed
      value: true
  gameServerTemplate:
    spec:
      containers:
        - image: registry.cn-hangzhou.aliyuncs.com/gs-demo/gameserver:network
          name: gameserver
          volumeMounts:
            - name: podinfo
              mountPath: /etc/podinfo
      volumes:
        - name: podinfo
          downwardAPI:
            items:
              - path: "network"
                fieldRef:
                  fieldPath: metadata.annotations['game.kruise.io/network-status']
```

字段说明:
- 在对应container字段中声明 volumeMounts，定义访问路径，此例中为 /etc/podinfo
- 在 gameServerTemplate.spec 里声明downwardAPI，文件名设置为 “network“，并指定使用 “game.kruise.io/network-status” 该annotation。注意annotation的key要使用单引号''，双引号pod将创建失败。

业务pod及容器创建成功后，在对应的/etc/podinfo路径下存在 network 文件，其中记录了序列化后的网络信息，该信息可通过json解码成对应structure，在程序获取相应字段使用。解码的sample如下（golang版本）

```go
package demo
import (
	"encoding/json"
	"github.com/openkruise/kruise-game/apis/v1alpha1"
    "os"
)

func getNetwork()  {
	network, err := os.ReadFile("/etc/podinfo/network")
	if err != nil {
		return
	}
	
	networkStatus := &v1alpha1.NetworkStatus{}

	err = json.Unmarshal(network, networkStatus)
	if err != nil {
		return
	}
	
	// 访问networkStatus各个字段
}

```
