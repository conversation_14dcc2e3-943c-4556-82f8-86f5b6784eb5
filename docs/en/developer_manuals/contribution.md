## Contribute to OpenKruiseGame
Welcome to the OpenKruiseGame community. Feel free to offer assistance, report issues, improve document quality, fix bugs, or introduce new features. See below for details about how to submit content to OpenKruiseGame.

## Submit issues and participate in scenario-based discussions
OpenKruiseGame is a very open community. Feel free to submit various types of issues. The following list shows the issue types:
* bug report
* feature request
* performance issues
* feature proposal
* feature design
* help wanted
* doc incomplete
* test improvement
* any questions on project


When you submit an issue, make sure that you have performed data masking to ensure the confidentiality of your information such as AccessKey.
## Contribute to codes and documents
Actions that can offer help to OpenKruiseGame are worth encouraging. You can submit what you expect to fix in a pull request.
* If you find a spelling error, fix it.
* If you find a code error, fix it.
* If you find the missing unit tests, fix the issue.
* If you find a document incomplete or with errors, update it.

## Need additional help
If you need help on other types of problems during cloud-native transformation of game servers, email us for further help. Email: <EMAIL>

## Become a core contributor to OpenKruiseGame
You are also very welcome to participate in OpenKruiseGame meetings to jointly determine the future development of OpenKruiseGame. As a sub-project of OpenKruise, OpenKruiseGame is also discussed in our bi-weekly meetings when we discuss about OpenKruise. For more information, see <a target="_blank" href="https://github.com/openkruise/kruise#community">Schedule</a>.